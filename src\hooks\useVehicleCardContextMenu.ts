
// src/hooks/useVehicleCardContextMenu.ts
'use client';

import { useState, useCallback } from 'react';
import type { Vehicle, Task } from '@/types';

interface VehicleCardContextMenuData {
  vehicle: Vehicle;
  task: Task;
}

interface UseVehicleCardContextMenuReturn {
  // Context Menu state
  isContextMenuOpen: boolean;
  contextMenuPosition: { x: number; y: number } | null;
  contextMenuData: VehicleCardContextMenuData | null;
  openContextMenu: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  closeContextMenu: () => void;

  // Delivery Order Details Modal state
  isDeliveryOrderDetailsModalOpen: boolean;
  selectedVehicleForDeliveryOrder: Vehicle | null;
  selectedTaskForDeliveryOrder: Task | null;
  openDeliveryOrderDetailsModal: (vehicle: Vehicle, task: Task) => void;
  closeDeliveryOrderDetailsModal: () => void;
}

export function useVehicleCardContextMenu(): UseVehicleCardContextMenuReturn {
  // Context Menu state
  const [isContextMenuOpen, setIsContextMenuOpen] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState<{ x: number; y: number } | null>(null);
  const [contextMenuData, setContextMenuData] = useState<VehicleCardContextMenuData | null>(null);

  // Delivery Order Details Modal state
  const [isDeliveryOrderDetailsModalOpen, setIsDeliveryOrderDetailsModalOpen] = useState(false);
  const [selectedVehicleForDeliveryOrder, setSelectedVehicleForDeliveryOrder] = useState<Vehicle | null>(null);
  const [selectedTaskForDeliveryOrder, setSelectedTaskForDeliveryOrder] = useState<Task | null>(null);

  const openContextMenu = useCallback((event: React.MouseEvent, vehicle: Vehicle, task: Task) => {
    event.preventDefault();
    event.stopPropagation();
    setContextMenuPosition({ x: event.clientX, y: event.clientY });
    setContextMenuData({ vehicle, task });
    setIsContextMenuOpen(true);
  }, []);

  const closeContextMenu = useCallback(() => {
    setIsContextMenuOpen(false);
    // Position and data will be reset on next open
  }, []);

  const openDeliveryOrderDetailsModal = useCallback((vehicle: Vehicle, task: Task) => {
    setSelectedVehicleForDeliveryOrder(vehicle);
    setSelectedTaskForDeliveryOrder(task);
    setIsDeliveryOrderDetailsModalOpen(true);
  }, []);

  const closeDeliveryOrderDetailsModal = useCallback(() => {
    setIsDeliveryOrderDetailsModalOpen(false);
    setSelectedVehicleForDeliveryOrder(null);
    setSelectedTaskForDeliveryOrder(null);
  }, []);

  return {
    isContextMenuOpen,
    contextMenuPosition,
    contextMenuData,
    openContextMenu,
    closeContextMenu,

    isDeliveryOrderDetailsModalOpen,
    selectedVehicleForDeliveryOrder,
    selectedTaskForDeliveryOrder,
    openDeliveryOrderDetailsModal,
    closeDeliveryOrderDetailsModal,
  };
}
