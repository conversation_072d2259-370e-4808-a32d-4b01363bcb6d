// src/components/sections/task-list/task-item-content.tsx
'use client';

import React, { useCallback, useMemo } from 'react';
import type { Task, Vehicle, InTaskVehicleCardStyle, StyleableColumnId, VehicleDisplayMode, TaskListDensityMode } from '@/types';
import { TaskItemHeader } from './task-item-header';
import { TaskItemDispatchView } from './task-item-dispatch-view';
import { cn } from '@/lib/utils';
import { Separator } from '@/components/ui/separator';

interface TaskItemContentProps {
  task: Task;
  taskVehicles: Vehicle[];
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  productionLineCount: number;
  onVehicleDispatchedToLine: (vehicleId: string, productionLineId: string, taskId: string) => void;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  density: Exclude<TaskListDensityMode, 'card' | ''>; // Ensured density is one of the valid options
  isDragOver: boolean;
  handleVehicleDrop: (event: React.DragEvent<HTMLDivElement>, productionLineId: string | null, taskId: string) => void; // productionLineId can be null
  setDragOverProductionLineId: (id: string | null) => void;
  dragOverProductionLineId: string | null;
  vehicleDisplayMode: VehicleDisplayMode;
  taskStatusFilter: string;
  getStatusLabelProps: (status?: Task['dispatchStatus']) => { label: string; className: string };
  onOpenVehicleCardContextMenu: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  onOpenDeliveryOrderDetailsForVehicle: (vehicleId: string, taskId: string, deliveryOrderId?: string) => void;
  onOpenStyleEditor: () => void;
  isParentDragTarget: boolean; // New prop to indicate if the parent (Card/List Item) is a drag target
  isSelected?: boolean; // New prop to indicate if the task is selected
}

export const TaskItemContent = React.memo(
  function TaskItemContent({
    task,
    taskVehicles,
    inTaskVehicleCardStyles,
    productionLineCount,
    onVehicleDispatchedToLine,
    onCancelVehicleDispatch,
    density,
    isDragOver,
    handleVehicleDrop,
    setDragOverProductionLineId,
    dragOverProductionLineId,
    vehicleDisplayMode,
    taskStatusFilter,
    getStatusLabelProps,
    onOpenVehicleCardContextMenu,
    onOpenDeliveryOrderDetailsForVehicle,
    onOpenStyleEditor,
    isParentDragTarget, // Consume new prop
    isSelected = false, // Consume new prop with default value
  }: TaskItemContentProps) {

    const showDispatchView = task.dispatchStatus === 'InProgress';

    // 使用useMemo缓存计算的样式类
    const { contentPaddingClass, separatorMarginClass } = useMemo(() => {
      let contentPaddingClass = 'p-0.5'; // Default for normal density
      let separatorMarginClass = 'my-0.5';
      
      if (density === 'compact') {
        contentPaddingClass = 'p-px';
        separatorMarginClass = 'my-px';
      } else if (density === 'loose') {
        contentPaddingClass = 'p-1';
        separatorMarginClass = 'my-1';
      }
      
      return { contentPaddingClass, separatorMarginClass };
    }, [density]);

    // 使用useCallback减少每次渲染时的函数重建
    const handleVehicleDropCallback = useCallback(
      (event: React.DragEvent<HTMLDivElement>, productionLineId: string) => {
        handleVehicleDrop(event, productionLineId, task.id);
      },
      [handleVehicleDrop, task.id]
    );

    const handleDragOverProductionLineIdChange = useCallback(
      (id: string | null) => {
        setDragOverProductionLineId(id);
      },
      [setDragOverProductionLineId]
    );

    const handleVehicleCardContextMenu = useCallback(
      (event: React.MouseEvent, vehicle: Vehicle) => {
        onOpenVehicleCardContextMenu(event, vehicle, task);
      },
      [onOpenVehicleCardContextMenu, task]
    );

    const handleDeliveryOrderDetailsForVehicle = useCallback(
      (vehicleId: string, taskId: string, deliveryOrderId?: string) => {
        onOpenDeliveryOrderDetailsForVehicle(vehicleId, taskId, deliveryOrderId);
      },
      [onOpenDeliveryOrderDetailsForVehicle]
    );

    // 只有需要时才渲染分隔符和调度视图
    const dispatchViewContent = useMemo(() => {
      if (!showDispatchView) return null;
      
      return (
        <>
          <Separator className={cn("border-dashed border-border", separatorMarginClass)} />
          <TaskItemDispatchView
            task={task}
            vehicles={taskVehicles}
            inTaskVehicleCardStyles={inTaskVehicleCardStyles}
            productionLineCount={productionLineCount}
            onVehicleDispatchedToLine={onVehicleDispatchedToLine}
            onCancelVehicleDispatch={onCancelVehicleDispatch}
            isDragOver={isDragOver}
            dragOverProductionLineId={dragOverProductionLineId}
            handleVehicleDrop={handleVehicleDropCallback}
            setDragOverProductionLineId={handleDragOverProductionLineIdChange}
            density={density}
            vehicleDisplayMode={vehicleDisplayMode}
            onOpenVehicleCardContextMenu={handleVehicleCardContextMenu}
            onOpenDeliveryOrderDetailsForVehicle={handleDeliveryOrderDetailsForVehicle}
            onOpenStyleEditor={onOpenStyleEditor}
          />
        </>
      );
    }, [
      showDispatchView,
      task,
      taskVehicles,
      inTaskVehicleCardStyles,
      productionLineCount,
      onVehicleDispatchedToLine,
      onCancelVehicleDispatch,
      isDragOver,
      dragOverProductionLineId,
      handleVehicleDropCallback,
      handleDragOverProductionLineIdChange,
      density,
      vehicleDisplayMode,
      handleVehicleCardContextMenu,
      handleDeliveryOrderDetailsForVehicle,
      onOpenStyleEditor,
      separatorMarginClass
    ]);

    const containerClasses = useMemo(() => 
      cn(
        "flex flex-col overflow-hidden",
        contentPaddingClass,
        isParentDragTarget && "border border-dashed border-primary"
      ), 
      [contentPaddingClass, isParentDragTarget]
    );

    return (
      <div className={containerClasses}>
        <TaskItemHeader
          task={task}
          getStatusLabelProps={getStatusLabelProps}
          density={density}
          isSelected={isSelected}
        />
        {dispatchViewContent}
      </div>
    );
  },
  // 自定义比较函数，进一步优化组件重渲染条件
  (prevProps, nextProps) => {
    // 任务ID相同且内部所有重要属性都相同时，视为不需要重渲染
    return prevProps.task.id === nextProps.task.id &&
           prevProps.isDragOver === nextProps.isDragOver &&
           prevProps.dragOverProductionLineId === nextProps.dragOverProductionLineId &&
           prevProps.isParentDragTarget === nextProps.isParentDragTarget &&
           prevProps.isSelected === nextProps.isSelected &&
           prevProps.density === nextProps.density &&
           prevProps.vehicleDisplayMode === nextProps.vehicleDisplayMode &&
           prevProps.taskVehicles.length === nextProps.taskVehicles.length &&
           JSON.stringify(prevProps.inTaskVehicleCardStyles) === JSON.stringify(nextProps.inTaskVehicleCardStyles);
  }
);

TaskItemContent.displayName = 'TaskItemContent';
