import type { Task, Vehicle, DeliveryOrder, Plant } from '@/types';

// API响应类型定义（根据你的后端API调整）
export interface ApiTask {
  id: string;
  plant_id: string;
  task_number: string;
  project_name: string;
  // ... 其他API字段，可能使用下划线命名
}

export interface ApiVehicle {
  id: string;
  vehicle_number: string;
  status: string;
  // ... 其他API字段
}

export interface ApiDeliveryOrder {
  id: string;
  plant_id: string;
  production_line_id: string;
  // ... 其他API字段
}

export interface ApiPlant {
  id: string;
  name: string;
  // ... 其他API字段
}

// 数据适配器类
export class DataAdapters {
  
  // 任务数据适配器
  static adaptTask(apiTask: ApiTask): Task {
    return {
      id: apiTask.id,
      plantId: apiTask.plant_id,
      taskNumber: apiTask.task_number,
      projectName: apiTask.project_name,
      // 添加默认值或转换逻辑
      projectAbbreviation: apiTask.project_name?.substring(0, 4) || '',
      constructionUnit: '',
      constructionSite: '',
      strength: '',
      pouringMethod: '',
      vehicleCount: 0,
      completedVolume: 0,
      requiredVolume: 0,
      pumpTruck: '',
      otherRequirements: '',
      contactPhone: '',
      supplyTime: '',
      supplyDate: '',
      publishDate: '',
      dispatchStatus: 'New',
      isTicketed: false,
      dispatchFrequencyMinutes: 30,
      lastDispatchTime: '',
      nextScheduledDispatchTime: '',
      isDueForDispatch: false,
      minutesToDispatch: 0,
      productionLineCount: 2,
      unreadMessageCount: 0,
      hasNewMessages: false,
      messages: [],
      deliveryStatus: '',
      dispatchReminderMinutes: 5,
      // 根据实际API响应调整映射
    } as Task;
  }

  // 车辆数据适配器
  static adaptVehicle(apiVehicle: ApiVehicle): Vehicle {
    return {
      id: apiVehicle.id,
      vehicleNumber: apiVehicle.vehicle_number,
      status: apiVehicle.status as Vehicle['status'],
      type: 'Tanker', // 默认值，根据API调整
      operationalStatus: 'normal',
      isDragging: false,
      // 根据实际API响应调整映射
    } as Vehicle;
  }

  // 配送单数据适配器
  static adaptDeliveryOrder(apiOrder: ApiDeliveryOrder): DeliveryOrder {
    return {
      id: apiOrder.id,
      plantId: apiOrder.plant_id,
      productionLineId: apiOrder.production_line_id,
      vehicleNumber: '',
      driver: '',
      volume: 0,
      strength: '',
      projectName: '',
      taskNumber: '',
      status: 'newlyDispatched',
      // 根据实际API响应调整映射
    } as DeliveryOrder;
  }

  // 搅拌站数据适配器
  static adaptPlant(apiPlant: ApiPlant): Plant {
    return {
      id: apiPlant.id,
      name: apiPlant.name,
      stats: {
        completedTasks: 0,
        totalTasks: 0,
      },
      productionLineCount: 2,
      // 根据实际API响应调整映射
    } as Plant;
  }

  // 批量适配器
  static adaptTasks(apiTasks: ApiTask[]): Task[] {
    return apiTasks.map(this.adaptTask);
  }

  static adaptVehicles(apiVehicles: ApiVehicle[]): Vehicle[] {
    return apiVehicles.map(this.adaptVehicle);
  }

  static adaptDeliveryOrders(apiOrders: ApiDeliveryOrder[]): DeliveryOrder[] {
    return apiOrders.map(this.adaptDeliveryOrder);
  }

  static adaptPlants(apiPlants: ApiPlant[]): Plant[] {
    return apiPlants.map(this.adaptPlant);
  }
}
