import React, { useCallback } from 'react';
import { TaskGroup, TaskGroupConfig } from '@/types';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  ChevronDown,
  ChevronRight,
  BarChart3,
  Users,
  CheckCircle,
  Clock,
  Truck,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TaskGroupHeaderProps {
  group: TaskGroup;
  groupConfig: TaskGroupConfig;
  onToggleCollapse?: (groupKey: string) => void;
  onCancelGrouping?: () => void;
}

export const TaskGroupHeader = React.memo(function TaskGroupHeader({
  group,
  groupConfig,
  onToggleCollapse,
  onCancelGrouping,
}: TaskGroupHeaderProps) {
  const { key, label, tasks, collapsed } = group;
  const { collapsible, showGroupStats, groupHeaderStyle } = groupConfig;

  const handleToggle = useCallback(() => {
    if (collapsible && onToggleCollapse) {
      onToggleCollapse(key);
    }
  }, [collapsible, onToggleCollapse, key]);

  /**
   * 处理右键菜单事件，用于取消分组
   */
  const handleContextMenu = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    if (onCancelGrouping) {
      onCancelGrouping();
    }
  }, [onCancelGrouping]);

 

  return (
    <div
      className={cn(
        'sticky top-0 z-10 border-b transition-all duration-200 max-h-[30px]',
        groupHeaderStyle.backgroundColor,
        groupHeaderStyle.textColor,
        groupHeaderStyle.fontSize,
        groupHeaderStyle.fontWeight,
        'px-1',
        collapsible ? 'cursor-pointer hover:bg-opacity-80' : ''
      )}
      onClick={collapsible ? handleToggle : undefined}
      onContextMenu={handleContextMenu}
      title="右键点击取消分组"
    >
      <div className="flex items-center justify-between min-h-[30px]">
        {/* 左侧：分组标题和折叠图标 */}
        <div className="flex items-center gap-3">
          {collapsible && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 hover:bg-background/20"
              onClick={(e) => {
                e.stopPropagation();
                handleToggle();
              }}
            >
              {collapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          )}
          
          <div className="flex items-center gap-2">
            <span className="font-medium">{label}</span>
            <Badge variant="outline" className="text-xs">
              {tasks.length} 项
            </Badge>
          </div>
        </div>

       
      </div>

      
    </div>
  );
});

/**
 * 简化版分组头部，用于紧凑显示
 */
export const CompactTaskGroupHeader = React.memo(function CompactTaskGroupHeader({
  group,
  groupConfig,
  onToggleCollapse,
  onCancelGrouping,
}: TaskGroupHeaderProps) {
  const { key, label, tasks, collapsed } = group;
  const { collapsible, groupHeaderStyle } = groupConfig;

  const handleToggle = useCallback(() => {
    if (collapsible && onToggleCollapse) {
      onToggleCollapse(key);
    }
  }, [collapsible, onToggleCollapse, key]);

  /**
   * 处理右键菜单事件，用于取消分组
   */
  const handleContextMenu = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    if (onCancelGrouping) {
      onCancelGrouping();
    }
  }, [onCancelGrouping]);

  return (
    <div
      className={cn(
        'flex items-center gap-2 py-1 px-2 border-b transition-all',
        groupHeaderStyle.backgroundColor,
        groupHeaderStyle.textColor,
        'text-sm font-medium',
        collapsible ? 'cursor-pointer hover:bg-opacity-80' : ''
      )}
      onClick={collapsible ? handleToggle : undefined}
      onContextMenu={handleContextMenu}
      title="右键点击取消分组"
    >
      {collapsible && (
        <Button
          variant="ghost"
          size="sm"
          className="h-4 w-4 p-0"
          onClick={(e) => {
            e.stopPropagation();
            handleToggle();
          }}
        >
          {collapsed ? (
            <ChevronRight className="h-3 w-3" />
          ) : (
            <ChevronDown className="h-3 w-3" />
          )}
        </Button>
      )}
      
      <span>{label}</span>
      <Badge variant="outline" className="text-xs ml-auto">
        {tasks.length}
      </Badge>
    </div>
  );
});
