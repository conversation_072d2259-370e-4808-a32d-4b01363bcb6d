# 项目进化步骤.md

## 第一阶段：基础重构与结构清理
### 细化组件粒度：

  1. 目标： 将大型、职责过多的组件（如 TaskList.tsx）拆分成更小、更专注于单一功能的子组件。
  2. 操作： 识别组件内部的独立逻辑块或渲染单元，将其提取为新的组件，并通过 props 传递必要的数据和事件。例如，从 TaskList 中拆分 TaskGroup, VehicleCard, DispatchButton 等。
  3. 参考： next.do.md 痛点/建议 1. 组件粒度与复用性
### 剥离组件内的业务逻辑：

  1. 目标： 将所有核心业务计算（如任务进度计算、车辆分配逻辑）、数据过滤/排序、以及复杂的副作用逻辑从组件内部移除。
  2. 操作： 将这些逻辑移动到 src/services 或 src/lib 中的相应模块。组件只负责调用这些服务层的方法，并根据结果更新 UI。
  3. 参考： next.do.md 痛点/建议 3. 业务逻辑与视图耦合
### 统一类型定义：

  1. 目标： 确保核心实体（如 Task, Vehicle, Plant, User 等）的类型定义集中、一致，并与后端接口返回的数据结构同步。
  2. 操作： 审查 src/types/index.ts 或创建新的类型文件，将所有相关的 TypeScript 接口和类型定义收敛于此。在使用这些类型的地方，直接导入 src/types。
  3. 参考： next.do.md 建议 4. 类型定义与数据流
### 集中通用工具函数：

1. 目标： 将散落在各处的公共函数（如格式化日期、处理字符串、基本计算等）统一归集管理。
2. 操作： 检查 src/lib/utils.ts 或创建类似的工具文件，将这些函数移动过去。组件或其他模块需要使用时，从这里导入。
3. 参考： next.do.md 建议 5. 模块解耦
## 第二阶段：状态管理与数据流优化

### 状态分层与模块化 Zustand Store：

1. 目标： 清晰区分 UI 状态和业务领域状态，并对 Zustand store 进行模块化管理，避免全局 store 过于臃肿。
2. 操作：
创建专门的 UI 状态 store，管理如模态框的开启/关闭、侧边栏状态、主题设置等。
为核心业务领域（如 Task, Vehicle）创建独立的 Zustand store 或使用 Context/Reducer 组合，内聚该领域的数据和相关的更新逻辑。
组件连接到它们所需特定领域的 store 或 UI store。
3. 参考： next.do.md 痛点/建议 2. 状态管理
### 优化数据获取与缓存：

1. 目标： 充分利用 React Query 等数据请求库的能力，优化数据获取、缓存、同步和错误处理。
2. 操作： 确保所有数据获取逻辑都通过 React Query 的 Hook (e.g., useQuery, useMutation) 进行，避免在组件内部直接调用 fetch 或 Axios。
3. 参考： next.do.md 痛点/建议 3. 业务逻辑与视图耦合 (数据获取是业务逻辑的一部分)
### 梳理组件间数据流和通信：

1. 目标： 减少 props drilling（通过多层组件传递数据），明确组件间的通信方式。
2. 操作：
对于需要在组件树中深层传递的数据，考虑使用 Context。
对于组件间的事件通知，可以利用 Context 提供的回调函数，或者在更复杂的场景下考虑使用事件总线（虽然对于中等复杂度项目可能不是必需）。
通过 Hook 或 Zustand store 传递状态和行为，而非层层传递 props。
3. 参考： next.do.md 建议 5. 模块解耦
## 第三阶段：进阶优化与系统增强

### 应用 Next.js App Router 特性：

目标： 充分利用 App Router 的布局、嵌套路由、Server Components 等特性，优化页面结构和数据加载。
操作： 根据页面结构规划 layout.tsx, page.tsx 文件。考虑哪些数据可以在 Server Component 中预取，减少客户端负载。
参考： next.do.md 建议 6. 路由与页面组织
### 实施性能优化措施：

目标： 提升应用在大数据量或复杂交互场景下的性能。
操作：
对于任务列表、车辆列表等，应用虚拟滚动 (virtualized-table)。
使用 React.lazy 和 Suspense 实现组件的动态导入。
将复杂的交互逻辑（如拖拽）抽象为独立的 Hook 或服务。
参考： next.do.md 建议 7. 性能与可扩展性
### 完善样式规范与主题系统：

目标： 统一项目视觉风格，支持主题切换。
操作： 检查并规范 tailwind.config.ts 的配置。如果需要主题切换，抽象一个主题 Context 或 Zustand store 来管理当前主题，并确保组件能够响应主题变化。
参考： next.do.md 建议 8. 样式与主题
### 增加测试覆盖与文档：

目标： 保证代码质量，便于团队协作和项目维护。
操作：
为核心业务逻辑（src/services, src/lib 中的函数）编写单元测试。
为关键的自定义 Hook 编写测试。
为重要组件编写 Storybook 或组件级别的测试。
为复杂的服务、Hook 和组件添加详细的注释和文档。
参考： next.do.md 建议 9. 测试与文档
执行策略：

逐步进行： 不必一次性完成所有步骤。可以从影响最大、最容易开始的部分着手，例如先剥离组件逻辑和统一类型。
小步提交： 每完成一个小的优化点就进行代码提交，方便回溯和协作。
团队协作： 如果是团队项目，需要团队成员共同理解和遵循新的架构原则。
持续改进： 架构优化是一个持续过程，随着项目发展，可能需要根据实际情况进行调整。
这份步骤列表是对 next.do.md 建议的具体化落地。遵循这些步骤将有助于项目达到更优的架构状态。