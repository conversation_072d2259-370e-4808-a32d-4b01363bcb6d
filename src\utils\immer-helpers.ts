/**
 * Immer 工具函数集合
 * 提供类型安全的不可变状态更新辅助函数
 */
import { produce, Draft } from 'immer';

/**
 * 创建一个类型安全的 produce 包装器
 * @param updater 状态更新函数
 * @returns 新的状态
 */
export function createImmerUpdater<T>(
  updater: (draft: Draft<T>) => void
) {
  return (state: T): T => produce(state, updater);
}

/**
 * 用于 React setState 的 Immer 包装器
 * @param updater 状态更新函数
 * @returns setState 兼容的函数
 */
export function immerSetState<T>(
  updater: (draft: Draft<T>) => void
) {
  return (prevState: T): T => produce(prevState, updater);
}

/**
 * 用于 Zustand 的 Immer 包装器
 * @param updater 状态更新函数
 * @returns Zustand set 兼容的函数
 */
export function immerZustand<T>(
  set: (partial: T | Partial<T> | ((state: T) => T | Partial<T>)) => void,
  updater: (draft: Draft<T>) => void
) {
  set((state) => produce(state, updater));
}