// src/components/sections/vehicle-dispatch.tsx
'use client';

import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { useDrag, useDrop, DropTargetMonitor } from 'react-dnd';
import type { Identifier, XYCoord } from 'dnd-core';
import type { Vehicle, VehicleDisplayMode, TaskListDensityMode, InTaskVehicleCardStyle } from '@/types'; // Added InTaskVehicleCardStyle
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuPortal,
} from '@/components/ui/dropdown-menu';
import { SectionContainer } from '@/components/shared/section-container';
import { IconButton } from '@/components/shared/icon-button';
import { InTaskVehicleCard } from './task-list/in-task-vehicle-card'; // Import the unified card
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { useTaskListSettings } from '@/hooks/useTaskListSettings'; // 导入 useTaskListSettings
import {
  PlayCircle,
  PauseCircle,
  XCircle,
  BarChartHorizontalBig,
  RotateCcw,
  ListTree,
  RectangleHorizontal,
  Rows,
  LayoutGrid,
} from 'lucide-react';
import { useAppStore } from '@/store/appStore';
import { useUiStore } from '@/store/uiStore';
import { shallow } from 'zustand/shallow';
import { getPendingVehicles, getReturnedVehicles, getOutboundVehicles } from '@/services/vehicleFilteringService';
import { ItemTypes } from '@/constants/dndItemTypes';

interface DispatchableVehicleCardWrapperProps {
  vehicle: Vehicle;
  index: number;
  listType: 'pending' | 'returned';
  globalDispatchActive: boolean;
  displayMode: VehicleDisplayMode;
  inTaskVehicleCardStyles: InTaskVehicleCardStyle; // Added
  density: Exclude<TaskListDensityMode, '' | 'card'>; // Added
  onContextMenu: (event: React.MouseEvent, vehicle: Vehicle) => void;
  onVisualMove: (draggedId: string, hoverId: string, statusList: 'pending' | 'returned') => void;
  onCommitReorder: (statusList: 'pending' | 'returned') => void;
}

const DispatchableVehicleCardWrapper = React.memo(({
  vehicle,
  index,
  listType,
  globalDispatchActive,
  displayMode,
  inTaskVehicleCardStyles, // Consumed
  density, // Consumed
  onContextMenu,
  onVisualMove,
  onCommitReorder,
}: DispatchableVehicleCardWrapperProps) => {
  const ref = useRef<HTMLDivElement>(null);

  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.VEHICLE_CARD_DISPATCH,
    item: { vehicle, index, statusList: listType, type: ItemTypes.VEHICLE_CARD_DISPATCH },
    canDrag: () => globalDispatchActive && 
                     (vehicle.status === 'pending' || vehicle.status === 'returned') && 
                     (vehicle.operationalStatus === 'normal' || !vehicle.operationalStatus),
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    end: (item, monitor) => {
      if (!monitor.didDrop() && item.statusList === listType) {
        onCommitReorder(listType);
      }
    }
  });

  const [, drop] = useDrop<
    { vehicle: Vehicle; index: number; statusList: 'pending' | 'returned'; type: string },
    void,
    { handlerId: Identifier | null }
  >({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    canDrop: (item) => item.statusList === listType,
    hover: (item, monitor) => {
      if (!ref.current) return;
      if (item.statusList !== listType) return;

      const dragIndex = item.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) return;

      const hoverBoundingRect = ref.current.getBoundingClientRect();
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;

      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;

      onVisualMove(item.vehicle.id, vehicle.id, listType);
      item.index = hoverIndex;
    },
  });

  drag(drop(ref));

  return (
    <div 
      ref={ref} 
      className={cn(isDragging && "opacity-40",  "relative z-10")}
      onContextMenu={(e) => {
        e.preventDefault();
        onContextMenu(e, vehicle);
      }}
    >
      <InTaskVehicleCard
        vehicle={vehicle}
        vehicleDisplayMode={displayMode}
        inTaskVehicleCardStyles={inTaskVehicleCardStyles}
        density={density}
        onOpenContextMenu={(e) => onContextMenu(e, vehicle)}
        isDragging={isDragging}
        // No task context here, so some InTaskVehicleCard features might not apply or need default handling
        isDispatchPanelView={true} // Indicate this card is in the dispatch panel
        listeners={undefined} // Explicitly pass undefined if not used
        attributes={undefined} // Explicitly pass undefined if not used
      />
    </div>
  );
});
DispatchableVehicleCardWrapper.displayName = 'DispatchableVehicleCardWrapper';


/**
 * 车辆调度组件
 * @returns React.FC
 */
export function VehicleDispatch() {
  const { toast } = useToast();
  const [contextMenuOpen, setContextMenuOpen] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState<{ x: number; y: number } | null>(null);
  const [contextMenuVehicle, setContextMenuVehicle] = useState<Vehicle | null>(null);

  const vehiclesFromStore = useAppStore(state => state.vehicles, shallow);
  const reorderVehiclesInListStoreAction = useAppStore(state => state.reorderVehiclesInList); 

  const taskStatusFilter = useUiStore(state => state.taskStatusFilter);
  const { vehicleDisplayMode, setVehicleDisplayMode, vehicleListDisplayMode, setVehicleListDisplayMode } = useUiStore(
    state => ({ 
      vehicleDisplayMode: state.vehicleDisplayMode, 
      setVehicleDisplayMode: state.setVehicleDisplayMode,
      vehicleListDisplayMode: state.vehicleListDisplayMode,
      setVehicleListDisplayMode: state.setVehicleListDisplayMode
    }),
    shallow
  );
  // 从 useTaskListSettings 获取车辆卡片样式和密度设置
  const { settings } = useTaskListSettings();
  const inTaskVehicleCardStyles = settings.inTaskVehicleCardStyles;
  const density = settings.density;


  const allVehicles = Array.isArray(vehiclesFromStore) ? vehiclesFromStore : [];

  const [pendingVehicles, setPendingVehicles] = useState(() => getPendingVehicles(allVehicles));
  const [returnedVehicles, setReturnedVehicles] = useState(() => getReturnedVehicles(allVehicles));
  const outboundVehicles = useMemo(() => getOutboundVehicles(allVehicles), [allVehicles]);

  useEffect(() => {
    setPendingVehicles(getPendingVehicles(allVehicles));
    setReturnedVehicles(getReturnedVehicles(allVehicles));
  }, [allVehicles]);

  const isDispatchActiveOverall = taskStatusFilter === 'InProgress';

  const handleVehicleContextMenu = useCallback((event: React.MouseEvent, vehicle: Vehicle) => {
    event.preventDefault();
    event.stopPropagation();
    setContextMenuPosition({ x: event.clientX, y: event.clientY });
    setContextMenuVehicle(vehicle);
    setContextMenuOpen(true);
  }, []);

  const handleCloseContextMenu = useCallback(() => {
    setContextMenuOpen(false);
    setContextMenuVehicle(null);
  }, []);

  const handleAction = useCallback((action: string, vehicle: Vehicle | null) => {
    if (!vehicle) return;
    const effectiveOperationalStatus = vehicle.operationalStatus || 'normal';
    toast({
      title: `操作: ${action}`,
      description: `车辆: ${vehicle.id} (${vehicle.vehicleNumber}), 操作状态: ${effectiveOperationalStatus}, 状态: ${vehicle.status}`,
    });
    handleCloseContextMenu();
  }, [toast, handleCloseContextMenu]);

  // 根据 inTaskVehicleCardStyles.vehiclesPerRow 动态计算网格列数
  const gridClass = `grid-cols-${inTaskVehicleCardStyles.vehiclesPerRow || 4}`;

  const toggleVehicleIdDisplayMode = useCallback(() => {
    setVehicleDisplayMode(vehicleDisplayMode === 'licensePlate' ? 'internalId' : 'licensePlate');
  }, [vehicleDisplayMode, setVehicleDisplayMode]);

  // Toggle between table and card view for outbound vehicles only
  const toggleVehicleListDisplayMode = useCallback(() => {
    setVehicleListDisplayMode(vehicleListDisplayMode === 'table' ? 'card' : 'table');
  }, [vehicleListDisplayMode, setVehicleListDisplayMode]);

  // 车辆调度标题行的内部编号/车牌号切换按钮
  const vehicleIdDisplayModeToggleButton = (
    <div className="flex gap-1">
      <IconButton
        icon={vehicleDisplayMode === 'licensePlate' ? ListTree : RectangleHorizontal}
        tooltipLabel={vehicleDisplayMode === 'licensePlate' ? "内部编号视图" : "车牌号视图"}
        onClick={toggleVehicleIdDisplayMode}
      />
    </div>
  );

  // 出厂车辆标题行的卡片/表格切换按钮
  const vehicleListDisplayModeToggleButton = (
    <div className="flex gap-1">
      <IconButton
        icon={vehicleListDisplayMode === 'table' ? LayoutGrid : Rows}
        tooltipLabel={vehicleListDisplayMode === 'table' ? "卡片视图" : "表格视图"}
        onClick={toggleVehicleListDisplayMode}
      />
    </div>
  );

  const handleVisualMoveCard = useCallback((draggedId: string, hoverId: string, statusList: 'pending' | 'returned') => {
    const listSetter = statusList === 'pending' ? setPendingVehicles : setReturnedVehicles;
    listSetter((prevList) => {
      const dragIndex = prevList.findIndex(v => v.id === draggedId);
      const hoverIndex = prevList.findIndex(v => v.id === hoverId);
      if (dragIndex === -1 || hoverIndex === -1) return prevList;
      
      const newList = [...prevList];
      const [draggedItem] = newList.splice(dragIndex, 1);
      newList.splice(hoverIndex, 0, draggedItem);
      return newList;
    });
  }, []);
  
  const handleCommitReorder = useCallback((statusList: 'pending' | 'returned') => {
      const currentList = statusList === 'pending' ? pendingVehicles : returnedVehicles;
      reorderVehiclesInListStoreAction(statusList, currentList.map(v => v.id));
  }, [pendingVehicles, returnedVehicles, reorderVehiclesInListStoreAction]);


  const renderVehicleListForStatus = useCallback((list: Vehicle[], listStatus: 'pending' | 'returned') => {
    // 待发车辆和退货车辆始终使用卡片视图
    return (
      <div className={cn("grid gap-1", gridClass)}>
        {list.map((vehicle, index) => (
          <DispatchableVehicleCardWrapper
            key={vehicle.id}
            vehicle={vehicle}
            index={index}
            listType={listStatus}
            globalDispatchActive={isDispatchActiveOverall}
            displayMode={vehicleDisplayMode}
            inTaskVehicleCardStyles={inTaskVehicleCardStyles} // Pass styles
            density={density} // Pass density
            onContextMenu={handleVehicleContextMenu}
            onVisualMove={handleVisualMoveCard}
            onCommitReorder={handleCommitReorder} 
          />
        ))}
      </div>
    );
  }, [gridClass, isDispatchActiveOverall, vehicleDisplayMode, inTaskVehicleCardStyles, density, handleVehicleContextMenu, handleVisualMoveCard, handleCommitReorder]);

  const renderContextMenuItems = useCallback((vehicle: Vehicle) => {
    const effectiveOperationalStatus = vehicle.operationalStatus || 'normal';
    const isDispatchPossibleContext =
      isDispatchActiveOverall &&
      (vehicle.status === 'pending' || vehicle.status === 'returned') &&
      effectiveOperationalStatus === 'normal';

    return (
      <>
        {isDispatchPossibleContext && (
          <DropdownMenuItem onClick={() => handleAction('dispatch', vehicle)}>
            <PlayCircle className="mr-1 h-3 w-3" />发车
          </DropdownMenuItem>
        )}
        {vehicle.status !== 'outbound' && effectiveOperationalStatus === 'paused' && (
          <DropdownMenuItem onClick={() => handleAction('resumeDispatch', vehicle)}>
            <PlayCircle className="mr-1 h-3 w-3" />恢复出车
          </DropdownMenuItem>
        )}
        {vehicle.status !== 'outbound' && effectiveOperationalStatus === 'normal' && (
          <DropdownMenuItem onClick={() => handleAction('pauseDispatch', vehicle)}>
            <PauseCircle className="mr-1 h-3 w-3" />暂停出车
          </DropdownMenuItem>
        )}
        {effectiveOperationalStatus !== 'deactivated' && (
          <DropdownMenuItem onClick={() => handleAction('deactivate', vehicle)}>
            <XCircle className="mr-1 h-3 w-3" />停用
          </DropdownMenuItem>
        )}
        {effectiveOperationalStatus === 'deactivated' && (
            <DropdownMenuItem onClick={() => handleAction('activate', vehicle)}>
            <RotateCcw className="mr-1 h-3 w-3" />重新启用
          </DropdownMenuItem>
        )}
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => handleAction('stats', vehicle)}><BarChartHorizontalBig className="mr-1 h-3 w-3" />单车出车情况统计</DropdownMenuItem>
      </>
    );
  }, [isDispatchActiveOverall, handleAction]);

  return (
      <SectionContainer
        title="车辆调度"
        actionButtons={vehicleIdDisplayModeToggleButton}
        className="h-full p-0"
        contentClassName={cn(
          "p-1 flex flex-col gap-1 [&>*]:pointer-events-auto pointer-events-none",
          !isDispatchActiveOverall && "opacity-60"
        )}
      
      >
        <Card className="flex-1 min-h-0 flex flex-col">
          <CardHeader
            className="py-0.5 px-1 border-b"
            style={{backgroundColor: 'hsl(var(--block-title-1))'}}
          >
            <CardTitle className="text-[11px] font-medium">待发车辆 ({pendingVehicles.length})</CardTitle>
          </CardHeader>
          <CardContent className="p-1 flex-1 overflow-y-auto custom-thin-scrollbar">
            {pendingVehicles.length > 0 ? renderVehicleListForStatus(pendingVehicles, 'pending') : <p className="text-[10px] text-muted-foreground text-center py-0.5">无待发车辆</p>}
          </CardContent>
        </Card>

        <Card className="flex-1 min-h-0 flex flex-col">
          <CardHeader
            className="py-0.5 px-1 border-b"
            style={{backgroundColor: 'hsl(var(--block-title-1))'}}
          >
            <CardTitle className="text-[11px] font-medium">退货车辆 ({returnedVehicles.length})</CardTitle>
          </CardHeader>
          <CardContent className="p-1 flex-1 overflow-y-auto custom-thin-scrollbar">
            {returnedVehicles.length > 0 ? renderVehicleListForStatus(returnedVehicles, 'returned') : <p className="text-[10px] text-muted-foreground text-center py-0.5">无退货车辆</p>}
          </CardContent>
        </Card>

        <Card className="flex-1 min-h-0 flex flex-col">
          <CardHeader
            className="py-0.5 px-1 border-b flex justify-between items-center"
            style={{backgroundColor: 'hsl(var(--block-title-1))'}}

          >
            <CardTitle className="text-[11px] font-medium">
              <div className='flex justify-end'>
                <div className='flex items-center'>
                  出厂车辆 ({outboundVehicles.length})
                </div>
                
               <div className='flex items-end'>
                 {vehicleListDisplayModeToggleButton}
               </div>
               </div>

              </CardTitle>
          </CardHeader>
          <CardContent className="p-1 flex-1 overflow-y-auto custom-thin-scrollbar">
              {outboundVehicles.length > 0 ?
                vehicleListDisplayMode === 'table' ? (
                  <div className="w-full overflow-x-auto">
                    <table className="w-full border-collapse text-xs">
                      <thead>
                        <tr className="bg-muted/50">
                          <th className="p-1 text-left font-medium">往返</th>
                          <th className="p-1 text-left font-medium">司机</th>
                          <th className="p-1 text-left font-medium">车牌号</th>
                          <th className="p-1 text-left font-medium">内部编号</th>
                          <th className="p-1 text-left font-medium">车辆类型</th>
                          <th className="p-1 text-left font-medium">规格</th>
                          <th className="p-1 text-left font-medium">操作</th>
                        </tr>
                      </thead>
                      <tbody>
                        {outboundVehicles.map((v, index) => (
                          <tr 
                            key={v.id} 
                            className="border-b border-muted hover:bg-muted/30"
                            onContextMenu={(e) => {
                              e.preventDefault();
                              handleVehicleContextMenu(e, v);
                            }}
                          >
                            <td className="p-1">
                              {v.currentTripType === 'returnLeg' ? (
                                <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-blue-100 text-blue-600">
                                  <RotateCcw className="h-3 w-3" />
                                </span>
                              ) : (
                                <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-green-100 text-green-600">
                                  <PlayCircle className="h-3 w-3" />
                                </span>
                              )}
                            </td>
                            <td className="p-1">司机{index % 10 + 1}</td>
                            <td className="p-1">{v.vehicleNumber}</td>
                            <td className="p-1">{v.id}</td>
                            <td className="p-1">{v.type || 'Tanker'}</td>
                            <td className="p-1">
                              {v.type === 'Pump' ? '37米' : '12方'}
                            </td>
                            <td className="p-1">
                              <button 
                                className="text-xs text-blue-500 hover:underline"
                                onClick={(e) => handleVehicleContextMenu(e, v)}
                              >
                                更多
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div 
                    className={cn("grid gap-1", gridClass)}
                    onContextMenu={(e) => e.preventDefault()}
                  >
                    {outboundVehicles.map(v => (
                      <div 
                        key={v.id}
                        onContextMenu={(e) => {
                          e.preventDefault();
                          handleVehicleContextMenu(e, v);
                        }}
                      >
                        <InTaskVehicleCard // Directly use InTaskVehicleCard for display-only outbound
                          vehicle={v}
                          vehicleDisplayMode={vehicleDisplayMode}
                          inTaskVehicleCardStyles={inTaskVehicleCardStyles}
                          density={density}
                          onOpenContextMenu={handleVehicleContextMenu} // Still allow context menu
                          isDispatchPanelView={true} // Indicate panel view for consistent styling if needed
                        />
                      </div>
                    ))}
                  </div>
                )
              : <p className="text-[10px] text-muted-foreground text-center py-0.5">无出厂车辆</p>}
          </CardContent>
        </Card>
      {contextMenuOpen && contextMenuPosition && contextMenuVehicle && (
        <DropdownMenu open={contextMenuOpen} onOpenChange={setContextMenuOpen}>
          <DropdownMenuTrigger asChild>
            <div style={{ position: 'fixed', left: contextMenuPosition.x, top: contextMenuPosition.y }} />
          </DropdownMenuTrigger>
          <DropdownMenuPortal>
            <DropdownMenuContent
              sideOffset={5}
              align="start"
              className="z-50 text-xs min-w-[150px]"
              onCloseAutoFocus={(e) => e.preventDefault()}
            >
              {renderContextMenuItems(contextMenuVehicle)}
            </DropdownMenuContent>
          </DropdownMenuPortal>
        </DropdownMenu>
      )}
    </SectionContainer>
  );
}
