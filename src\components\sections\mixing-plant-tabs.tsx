
'use client';
import React, { useState, useCallback } from 'react';
import type { Plant } from '@/types';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RingProgress } from '@/components/shared/ring-progress';
import { cn } from '@/lib/utils';

interface MixingPlantTabsProps {
  plants: Plant[];
  selectedPlantId: string | null;
  onSelectPlant: (plantId: string) => void;
  onInitiateCrossPlantDispatch: (vehicleId: string, targetPlantId: string) => void;
}

export function MixingPlantTabs({
  plants,
  selectedPlantId,
  onSelectPlant,
  onInitiateCrossPlantDispatch
}: MixingPlantTabsProps) {
  const [dragOverPlantId, setDragOverPlantId] = useState<string | null>(null);

  const handleDropOnTab = useCallback((event: React.DragEvent<HTMLButtonElement>, targetPlantId: string) => {
    event.preventDefault();
    setDragOverPlantId(null);
    const vehicleId = event.dataTransfer.getData('text/plain');
    if (vehicleId && targetPlantId && targetPlantId !== selectedPlantId) {
      onInitiateCrossPlantDispatch(vehicleId, targetPlantId);
    }
  }, [selectedPlantId, onInitiateCrossPlantDispatch]);

  const handleDragEnterTab = useCallback((event: React.DragEvent<HTMLButtonElement>, plantId: string) => {
    event.preventDefault();
    if (plantId !== selectedPlantId && event.dataTransfer.types.includes('text/plain')) {
      setDragOverPlantId(plantId);
    }
  }, [selectedPlantId]);

  const handleDragLeaveTab = useCallback((event: React.DragEvent<HTMLButtonElement>) => {
    event.preventDefault();
    setDragOverPlantId(null);
  }, []);

  const handleDragOverTab = useCallback((event: React.DragEvent<HTMLButtonElement>, plantId: string) => {
    event.preventDefault();
    if (plantId !== selectedPlantId && event.dataTransfer.types.includes('text/plain')) {
      event.dataTransfer.dropEffect = 'move';
    } else {
      event.dataTransfer.dropEffect = 'none';
    }
  }, [selectedPlantId]);

  return (
    <Tabs value={selectedPlantId || undefined} onValueChange={onSelectPlant} className="w-auto">
      <TabsList className="bg-transparent p-0 h-auto border-b flex space-x-1">
        {plants.map((plant) => (
          <TabsTrigger
            key={plant.id}
            value={plant.id}
            className={cn(
              "px-4 pt-2 pb-[calc(0.5rem+2px)] text-xs transition-all rounded-none focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none relative",
              "border-b-2", 
              "data-[state=inactive]:text-muted-foreground data-[state=inactive]:border-transparent",
              "data-[state=inactive]:hover:text-primary data-[state=inactive]:hover:border-primary/30", 
              "data-[state=active]:text-primary data-[state=active]:font-semibold data-[state=active]:border-primary",
              dragOverPlantId === plant.id && plant.id !== selectedPlantId && "bg-accent/10 rounded-t-md"
            )}
            onDrop={(e) => handleDropOnTab(e, plant.id)}
            onDragOver={(e) => handleDragOverTab(e, plant.id)}
            onDragEnter={(e) => handleDragEnterTab(e, plant.id)}
            onDragLeave={handleDragLeaveTab}
            style={{ marginBottom: '-1px' }} 
          >
            <div className="flex items-center space-x-1.5">
              <RingProgress
                value={(plant.stats.totalTasks > 0 ? (plant.stats.completedTasks / plant.stats.totalTasks) : 0) * 100}
                radius={7}
                strokeWidth={2}
                label={plant.stats.totalTasks > 0 ? String(Math.round((plant.stats.completedTasks / plant.stats.totalTasks) * 100) + '%') : '0%'}
              />
              <span>{plant.name}</span>
            </div>
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
}
