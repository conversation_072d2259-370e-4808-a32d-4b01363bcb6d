// src/components/test/TaskRowHighlightTest.tsx
'use client';

import React from 'react';
import { useTaskHighlight } from '@/contexts/TaskRowHighlightContext';
import { useTaskSelectionState, useTaskSelectionActions, TaskSelectionProvider } from '@/contexts/TaskSelectionContext';
import { TaskRowHighlightProvider } from '@/contexts/TaskRowHighlightContext';

const TaskRowHighlightTestContent: React.FC = () => {
  const { highlightedTaskId, setHighlightedTaskId } = useTaskHighlight();
  const { selectedTaskId, selectedTask } = useTaskSelectionState();
  const { setSelectedTask, clearSelection } = useTaskSelectionActions();

  const testTasks = [
    { id: 'task-1', name: '任务 1', taskNumber: 'T001', dispatchStatus: 'InProgress' as const },
    { id: 'task-2', name: '任务 2', taskNumber: 'T002', dispatchStatus: 'Pending' as const },
    { id: 'task-3', name: '任务 3', taskNumber: 'T003', dispatchStatus: 'InProgress' as const },
  ];

  return (
    <div className="p-4 space-y-4">
      <h3 className="text-lg font-semibold">任务行高亮测试</h3>

      <div className="bg-yellow-100 p-3 rounded border">
        <p className="text-sm">
          <strong>测试说明:</strong>
          1. 点击任务行/卡片可以选中任务，再次点击取消选中<br/>
          2. 悬停在"悬停测试"按钮上可以高亮任务行<br/>
          3. 观察固定列是否保持正确位置，不会因为高亮或选中效果而移位<br/>
          4. <strong>颜色区别:</strong> 选中状态使用accent颜色(通常是蓝色)，拖拽高亮使用primary颜色<br/>
          5. 选中状态和高亮状态可以同时存在，会显示混合效果
        </p>
      </div>
      
      <div className="space-y-2">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="font-medium">当前高亮任务: {highlightedTaskId || '无'}</p>
            <div className="flex gap-2 mt-2">
              {testTasks.map(task => (
                <button
                  key={task.id}
                  onClick={() => setHighlightedTaskId(task.id)}
                  className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
                >
                  高亮 {task.name}
                </button>
              ))}
              <button
                onClick={() => setHighlightedTaskId(null)}
                className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 text-sm"
              >
                清除高亮
              </button>
            </div>
          </div>

          <div>
            <p className="font-medium">当前选中任务: {selectedTask ? `${selectedTask.taskNumber} (${selectedTask.name})` : '无'}</p>
            <div className="flex gap-2 mt-2">
              {testTasks.map(task => (
                <button
                  key={task.id}
                  onClick={() => setSelectedTask(task as any)}
                  className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 text-sm"
                >
                  选中 {task.name}
                </button>
              ))}
              <button
                onClick={clearSelection}
                className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 text-sm"
              >
                清除选中
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium">状态颜色对比:</h4>
        <div className="grid grid-cols-3 gap-4">
          <div className="p-3 border rounded">
            <div className="text-sm font-medium mb-2">普通状态</div>
            <div className="h-8 bg-white border rounded"></div>
          </div>
          <div className="p-3 border rounded">
            <div className="text-sm font-medium mb-2">拖拽高亮 (Primary)</div>
            <div className="h-8 rounded task-row-highlight"></div>
          </div>
          <div className="p-3 border rounded">
            <div className="text-sm font-medium mb-2">选中状态 (Accent)</div>
            <div className="h-8 rounded task-row-selected"></div>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium">模拟任务卡片:</h4>
        <div className="grid grid-cols-3 gap-2">
          {testTasks.map(task => (
            <div
              key={task.id}
              data-task-id={task.id}
              onClick={() => {
                // 切换选中状态
                if (selectedTaskId === task.id) {
                  clearSelection();
                } else {
                  setSelectedTask(task as any);
                }
              }}
              className={`p-3 border rounded bg-white cursor-pointer hover:bg-gray-50 task-card task-card-clickable ${
                selectedTaskId === task.id ? 'task-row-selected' : ''
              }`}
            >
              <div className="font-medium text-sm">{task.name}</div>
              <div className="text-xs text-gray-500">{task.taskNumber}</div>
              <div className="text-xs text-gray-400">状态: {task.dispatchStatus}</div>
            </div>
          ))}
        </div>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium">模拟表格行 (带固定列):</h4>
        <div className="overflow-x-auto border rounded" style={{ maxWidth: '600px' }}>
          <table className="border-collapse border min-w-[800px]">
          <thead>
            <tr className="bg-gray-100">
              <th className="border p-2 sticky left-0 bg-white z-10">固定列</th>
              <th className="border p-2">任务名称</th>
              <th className="border p-2">状态</th>
              <th className="border p-2">操作</th>
              <th className="border p-2 sticky right-0 bg-white z-10">固定操作</th>
            </tr>
          </thead>
          <tbody>
            {testTasks.map(task => (
              <tr
                key={task.id}
                data-task-id={task.id}
                onClick={() => {
                  // 切换选中状态
                  if (selectedTaskId === task.id) {
                    clearSelection();
                  } else {
                    setSelectedTask(task as any);
                  }
                }}
                className={`border cursor-pointer hover:bg-gray-50 ${
                  selectedTaskId === task.id ? 'task-row-selected' : ''
                }`}
              >
                <td
                  className="border p-2 sticky left-0 bg-white z-10"
                  style={{ position: 'sticky', left: '0px', backgroundColor: 'white' }}
                >
                  固定{task.id.slice(-1)}
                </td>
                <td className="border p-2">{task.name}</td>
                <td className="border p-2">进行中</td>
                <td className="border p-2">
                  <button
                    onMouseEnter={() => setHighlightedTaskId(task.id)}
                    onMouseLeave={() => setHighlightedTaskId(null)}
                    className="px-2 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
                  >
                    悬停测试
                  </button>
                </td>
                <td
                  className="border p-2 sticky right-0 bg-white z-10"
                  style={{ position: 'sticky', right: '0px', backgroundColor: 'white' }}
                >
                  <button className="px-2 py-1 bg-blue-500 text-white rounded text-sm">
                    固定操作
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        </div>
      </div>
    </div>
  );
};

// 包装器组件，提供必要的Context
export const TaskRowHighlightTest: React.FC = () => {
  return (
    <TaskSelectionProvider>
      <TaskRowHighlightProvider>
        <TaskRowHighlightTestContent />
      </TaskRowHighlightProvider>
    </TaskSelectionProvider>
  );
};
