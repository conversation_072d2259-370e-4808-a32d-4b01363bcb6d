# Mock数据替换为真实数据指南

## 概述

本指南介绍如何将项目中的Mock数据无缝替换为真实API数据，支持开发和生产环境的灵活切换。

## 架构设计

### 核心组件

1. **API配置系统** (`src/config/api.ts`)
   - 统一管理API端点和环境配置
   - 支持开发、生产、Mock三种模式

2. **HTTP客户端** (`src/services/httpClient.ts`)
   - 基于fetch的统一HTTP客户端
   - 自动处理Mock数据拦截
   - 错误处理和超时控制

3. **数据适配器** (`src/services/dataAdapters.ts`)
   - 处理API响应到前端数据模型的转换
   - 支持字段映射和默认值设置

4. **数据源切换器** (`src/utils/dataSourceSwitcher.ts`)
   - 运行时切换Mock/API数据源
   - 开发工具集成

## 使用方法

### 1. 环境配置

创建 `.env.local` 文件：

```bash
# 使用Mock数据（开发阶段）
NEXT_PUBLIC_USE_MOCK_DATA=true
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api

# 使用真实API（测试/生产阶段）
NEXT_PUBLIC_USE_MOCK_DATA=false
NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com
```

### 2. 开发时切换数据源

#### 方法一：环境变量
修改 `.env.local` 中的 `NEXT_PUBLIC_USE_MOCK_DATA` 值

#### 方法二：开发工具
在开发环境下，右下角的开发工具面板提供一键切换功能

#### 方法三：代码切换
```typescript
import { DataSourceSwitcher } from '@/utils/dataSourceSwitcher';

// 切换到API模式
DataSourceSwitcher.setMode('api');

// 切换到Mock模式  
DataSourceSwitcher.setMode('mock');
```

### 3. API适配

当你的后端API准备好后，需要更新数据适配器：

1. **更新API响应类型** (`src/services/dataAdapters.ts`)
```typescript
export interface ApiTask {
  id: string;
  plant_id: string;        // 后端字段名
  task_number: string;
  project_name: string;
  // ... 根据实际API调整
}
```

2. **更新适配器映射**
```typescript
static adaptTask(apiTask: ApiTask): Task {
  return {
    id: apiTask.id,
    plantId: apiTask.plant_id,     // 字段名转换
    taskNumber: apiTask.task_number,
    projectName: apiTask.project_name,
    // ... 其他字段映射
  } as Task;
}
```

### 4. API端点配置

在 `src/config/api.ts` 中配置你的API端点：

```typescript
endpoints: {
  tasks: '/tasks',
  vehicles: '/vehicles',
  deliveryOrders: '/delivery-orders',
  plants: '/plants',
}
```

## 部署配置

### 开发环境
```bash
NEXT_PUBLIC_USE_MOCK_DATA=true
```

### 测试环境
```bash
NEXT_PUBLIC_USE_MOCK_DATA=false
NEXT_PUBLIC_API_BASE_URL=https://test-api.yourdomain.com
```

### 生产环境
```bash
NEXT_PUBLIC_USE_MOCK_DATA=false
NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com
```

## 优势

1. **零停机切换** - 无需修改业务代码
2. **渐进式迁移** - 可以逐个API替换
3. **开发友好** - 支持运行时切换
4. **类型安全** - 完整的TypeScript支持
5. **错误处理** - 统一的错误处理机制

## 注意事项

1. **数据格式** - 确保API响应格式与适配器匹配
2. **认证** - 根据需要在httpClient中添加认证逻辑
3. **缓存** - React Query已配置，无需额外处理
4. **错误处理** - 生产环境建议添加更详细的错误处理

## 故障排除

### 常见问题

1. **数据不显示**
   - 检查环境变量配置
   - 查看浏览器控制台错误
   - 确认API端点是否正确

2. **类型错误**
   - 更新数据适配器中的类型定义
   - 检查API响应格式

3. **网络错误**
   - 检查API服务是否运行
   - 确认CORS配置
   - 检查网络连接

### 调试技巧

1. 使用开发工具查看当前数据源模式
2. 检查Network面板的API请求
3. 在适配器中添加console.log调试
4. 使用React Query DevTools查看缓存状态
