// src/services/taskFilteringService.ts
import type { Task } from '@/types';

/**
 * Filters tasks based on selected plant ID and task status.
 * @param tasks - The array of all tasks.
 * @param selectedPlantId - The ID of the currently selected plant, or null if none selected.
 * @param taskStatusFilter - The status to filter tasks by (e.g., 'InProgress', 'Completed').
 *                           If 'All' or empty, no status filtering is applied.
 * @returns An array of filtered tasks.
 */
export function filterTasksForDisplay(
  tasks: Task[],
  selectedPlantId: string | null,
  taskStatusFilter: string
): Task[] {
  if (!Array.isArray(tasks)) return [];

  let filtered = tasks;

  if (selectedPlantId) {
    filtered = filtered.filter(task => task.plantId === selectedPlantId);
  }

  if (taskStatusFilter && taskStatusFilter !== "All" && taskStatusFilter !== "") {
    // Ensure we handle the case where task.dispatchStatus might be undefined,
    // though it's typed as required. This is defensive.
    filtered = filtered.filter(task => task.dispatchStatus && task.dispatchStatus === taskStatusFilter);
  }
  return filtered;
}
