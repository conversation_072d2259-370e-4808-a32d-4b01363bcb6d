import { createWithEqualityFn } from 'zustand/traditional';
import type { Task, Vehicle, DeliveryOrder, ReminderWorkerInput, ReminderWorkerOutput, ReminderMessage } from '@/types';
import {
  getTasks as fetchDataServiceTasks,
  getVehicles as fetchDataServiceVehicles,
  getDeliveryOrders as fetchDataServiceDeliveryOrders,
  dispatchVehicleToTaskService,
  cancelVehicleDispatchService,
  // reorderVehiclesInListService is now handled by updating store based on new local order
  confirmCrossPlantDispatchService
} from '@/services/dataService';
// Removed: import { arrayMove } from '@dnd-kit/sortable'; // No longer needed
import { produce } from 'immer';

const REMINDER_WINDOW_MINUTES = 5;
const GLOBAL_TIMER_INTERVAL_MS = 5000;

interface WorkerApiType {
  postMessage: (message: ReminderWorkerInput) => void;
  then: (onResolve: (message: ReminderWorkerOutput) => void) => void;
  terminate: () => void;
}

interface AppState {
  selectedPlantId: any;
  tasks: Task[];
  vehicles: Vehicle[];
  deliveryOrders: DeliveryOrder[];

  fetchInitialData: () => Promise<void>;
  setMockTasks: (tasks: Task[]) => void;

  dispatchVehicleToTask: (vehicleId: string, taskId: string, productionLineId?: string) => Promise<Vehicle | null>;
  cancelVehicleDispatch: (vehicleId: string) => Promise<boolean>;
  // Updated signature: takes the status list type and the new array of ordered IDs for that list
  reorderVehiclesInList: (statusList: 'pending' | 'returned', newOrderedIds: string[]) => void;
  confirmCrossPlantDispatch: (vehicleId: string, targetPlantId: string, notes?: string) => Promise<void>;

  _workerApi: WorkerApiType | null;
  _globalTimerId: NodeJS.Timeout | null;
  _visibilityChangeHandler: (() => void) | null;
  initializeTaskManager: () => void;
  cleanupTaskManager: () => void;
  triggerWorkerUpdate: () => void;
}

export const useAppStore = createWithEqualityFn<AppState>((set, get) => ({
  tasks: [],
  vehicles: [],
  deliveryOrders: [],
  selectedPlantId: undefined,
  _workerApi: null,
  _globalTimerId: null,
  _visibilityChangeHandler: null,

  fetchInitialData: async () => {
    try {
      const [tasksData, vehiclesData, deliveryOrdersData] = await Promise.all([
        fetchDataServiceTasks(),
        fetchDataServiceVehicles(),
        fetchDataServiceDeliveryOrders(),
      ]);
      
      set({
        tasks: tasksData.map(t => ({...t, isDueForDispatch: false, nextScheduledDispatchTime: undefined})),
        vehicles: vehiclesData,
        deliveryOrders: deliveryOrdersData,
      });
    } catch (error) {
      console.error("Failed to fetch initial data (tasks, vehicles, deliveryOrders):", error);
      set({
        tasks: [],
        vehicles: [],
        deliveryOrders: [],
      });
    }
  },

  setMockTasks: (tasks: Task[]) => {
    set({ tasks });
    setTimeout(() => {
      get().triggerWorkerUpdate();
    }, 100);
  },

  initializeTaskManager: () => {
    if (get()._globalTimerId) {
      clearInterval(get()._globalTimerId as NodeJS.Timeout);
    }
    const scheduleUpdate = () => {
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => {
          get().triggerWorkerUpdate();
          scheduleUpdate(); // 递归调度
        }, { timeout: GLOBAL_TIMER_INTERVAL_MS });
      } else {
        setTimeout(() => {
          get().triggerWorkerUpdate();
          scheduleUpdate();
        }, GLOBAL_TIMER_INTERVAL_MS);
      }
    };
    
    scheduleUpdate();

    if (typeof document !== 'undefined' && !get()._visibilityChangeHandler) {
        const handler = () => {
          if (document.hidden) {
            if (get()._globalTimerId) clearInterval(get()._globalTimerId as NodeJS.Timeout);
            set({ _globalTimerId: null });
          } else {
            if (!get()._globalTimerId) {
                const newTimerId = setInterval(() => get().triggerWorkerUpdate(), GLOBAL_TIMER_INTERVAL_MS);
                set({ _globalTimerId: newTimerId });
                get().triggerWorkerUpdate();
            }
          }
        };
        document.addEventListener("visibilitychange", handler);
        set({ _visibilityChangeHandler: handler });
    }
    if (typeof document !== 'undefined' && !document.hidden) {
        get().triggerWorkerUpdate();
    }
  },

  triggerWorkerUpdate: () => {
    const workerApi = get()._workerApi;
    if (workerApi && workerApi.postMessage) {
      const currentTasks = get().tasks;
      const input: ReminderWorkerInput = {
        tasks: currentTasks,
        currentTime: Date.now(),
        reminderWindowMinutes: REMINDER_WINDOW_MINUTES,
      };
      workerApi.postMessage(input);
    }
  },

  cleanupTaskManager: () => {
    if (get()._globalTimerId) {
      clearInterval(get()._globalTimerId as NodeJS.Timeout);
    }
    const workerApi = get()._workerApi;
    if (workerApi && workerApi.terminate) {
      workerApi.terminate();
    }
    if (typeof document !== 'undefined' && get()._visibilityChangeHandler) {
        document.removeEventListener("visibilitychange", get()._visibilityChangeHandler!);
        set({ _visibilityChangeHandler: null });
    }
    set({ _globalTimerId: null, _workerApi: null });
  },

  dispatchVehicleToTask: async (vehicleId, taskId, productionLineId) => {
  const currentVehicles = get().vehicles;
  const effectiveProductionLineId = productionLineId || 'L1';
  
  const updatedVehicle = await dispatchVehicleToTaskService(currentVehicles, vehicleId, taskId, effectiveProductionLineId);
  if (updatedVehicle) {
    set(produce((draft) => {
      const vehicle = draft.vehicles.find((v: { id: string; }) => v.id === vehicleId);
      if (vehicle) {
        Object.assign(vehicle, updatedVehicle);
      }
      
      const task = draft.tasks.find((t: { id: string; }) => t.id === taskId);
      if (task) {
        const now = Date.now();
        const dispatchFrequencyMinutes = task.dispatchFrequencyMinutes || 30;
        const nextDispatchTime = new Date(now + dispatchFrequencyMinutes * 60 * 1000);
        
        task.lastDispatchTime = new Date(now).toISOString();
        task.nextScheduledDispatchTime = nextDispatchTime.toISOString();
        task.isDueForDispatch = false;
      }
    }));
    
    get().triggerWorkerUpdate();
    return updatedVehicle;
  }
  return null;
},


  cancelVehicleDispatch: async (vehicleId) => {
    const currentVehicles = get().vehicles;
    const updatedVehicle = await cancelVehicleDispatchService(currentVehicles, vehicleId);
    if (updatedVehicle) {
        set(state => ({
            vehicles: state.vehicles.map(v => v.id === vehicleId ? updatedVehicle : v)
        }));
        get().triggerWorkerUpdate();
        return true;
    } else {
      console.error(`Store: Failed to cancel dispatch for vehicle ${vehicleId}`);
      return false;
    }
  },

  // 优化车辆重新排序
reorderVehiclesInList: (statusList: 'pending' | 'returned', newOrderedIds: string[]) => {
  set(produce((draft) => {
    // 分离目标状态的车辆和其他车辆
    const vehiclesOfTargetStatus: Vehicle[] = [];
    const otherVehicles: Vehicle[] = [];
    
    draft.vehicles.forEach((vehicle: Vehicle) => {
      if (vehicle.status === statusList) {
        vehiclesOfTargetStatus.push(vehicle);
      } else {
        otherVehicles.push(vehicle);
      }
    });
    
    // 按新顺序重新排列
    const reorderedVehicles = newOrderedIds
      .map(id => vehiclesOfTargetStatus.find(v => v.id === id))
      .filter(Boolean) as Vehicle[];
    
    // 更新车辆数组
    draft.vehicles = [...otherVehicles, ...reorderedVehicles];
  }));
},

  confirmCrossPlantDispatch: async (vehicleId: string, targetPlantId: string, notes?: string) => {
    const currentVehicles = get().vehicles;
    const updatedVehicle = await confirmCrossPlantDispatchService(currentVehicles, vehicleId, targetPlantId, notes);
    if (updatedVehicle) {
      set(state => ({
        vehicles: state.vehicles.map(v => v.id === vehicleId ? updatedVehicle : v)
      }));
      get().triggerWorkerUpdate();
    } else {
      console.error(`Store: Failed to confirm cross-plant dispatch for vehicle ${vehicleId}`);
    }
  }
}), Object.is);

export const handleWorkerMessage = (workerOutput: ReminderWorkerOutput) => {
  const appStore = useAppStore.getState();
  const tasks = appStore.tasks;
  const { useUiStore } = require('@/store/uiStore'); 
  const uiStore = useUiStore.getState();

  if (workerOutput.error) {
    console.error('Worker error:', workerOutput.error);
    return;
  }

  if (workerOutput.recovery) {
    console.log('Worker recovery message received, attempting to restart...');
    setTimeout(() => {
      appStore.triggerWorkerUpdate();
    }, 1000);
    return;
  }

  if (workerOutput.messages && Array.isArray(workerOutput.messages)) {
    workerOutput.messages.forEach((message: ReminderMessage) => {
      const hasSoundMessages = message.type === 'sound';
      if (hasSoundMessages) {
        uiStore.incrementAlertSoundCounter();
      }
    });
  }

  const { tasksWithUpdates } = workerOutput;
  if (!tasksWithUpdates || tasksWithUpdates.length === 0) return;

  const updatedTasks = [...tasks];
  let shouldPlaySound = false;

  tasksWithUpdates.forEach(update => {
    const taskIndex = updatedTasks.findIndex(t => t.id === update.id);
    if (taskIndex === -1) return;

    const oldTask = updatedTasks[taskIndex];
    const oldIsDueForDispatch = oldTask.isDueForDispatch;
    const newIsDueForDispatch = update.isDueForDispatch;

    updatedTasks[taskIndex] = {
      ...oldTask,
      nextScheduledDispatchTime: update.nextScheduledDispatchTime,
      isDueForDispatch: update.isDueForDispatch ?? oldTask.isDueForDispatch,
      minutesToDispatch: update.minutesToDispatch
    };

    if (!oldIsDueForDispatch && newIsDueForDispatch) {
      shouldPlaySound = true;
    }
  });

  useAppStore.setState({ tasks: updatedTasks });

  if (shouldPlaySound) {
    uiStore.incrementAlertSoundCounter();
  }

  if (workerOutput.stats) {
    console.log(
      `Worker stats: processed ${workerOutput.stats.taskCount} tasks, ` +
      `updated ${workerOutput.stats.updatedCount} tasks in ` +
      `${workerOutput.stats.processingTimeMs.toFixed(2)}ms`
    );
  }
};
