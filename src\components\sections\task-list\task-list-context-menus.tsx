// src/components/sections/task-list/task-list-context-menus.tsx
'use client';

import React from 'react';
import type { Task, Vehicle } from '@/types';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuPortal,
} from '@/components/ui/dropdown-menu';
import {
  Eye, Edit, ListChecks, BarChartHorizontalBig, AlertOctagon, Trash2, Info, Copy, Send, Undo2, SlidersHorizontal, FileEditIcon, Bell,
} from 'lucide-react';

interface TaskContextMenuProps {
  isOpen: boolean;
  position: { x: number; y: number } | null;
  taskData: { taskId: string } | null;
  onClose: () => void;
  onOpenTankerNoteModal: (task: Task) => void;
  onOpenReminderConfigModal: (task: Task) => void;
  filteredTasks: Task[]; // To find the actual task object
}

const TaskContextMenu: React.FC<TaskContextMenuProps> = ({
  isOpen,
  position,
  taskData,
  onClose,
  onOpenTankerNoteModal,
  onOpenReminderConfigModal,
  filteredTasks,
}) => {
  if (!isOpen || !position || !taskData) return null;
  const task = filteredTasks.find(t => t.id === taskData.taskId);
  if (!task) return null;

  return (
    <DropdownMenu open={isOpen} onOpenChange={onClose}>
      <DropdownMenuTrigger asChild>
        <div style={{ position: 'fixed', left: position.x, top: position.y }} />
      </DropdownMenuTrigger>
      <DropdownMenuPortal>
        <DropdownMenuContent
          sideOffset={5}
          align="start"
          className="z-50 text-xs w-48"
          onCloseAutoFocus={(e) => e.preventDefault()}
        >
          <DropdownMenuItem onClick={() => onOpenTankerNoteModal(task)}>
            <ListChecks className="mr-2 h-3 w-3" />查看/编辑罐车发车单
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => console.log('Open task details:', task.id)}>
            <Eye className="mr-2 h-3 w-3" />查看任务详情
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => console.log('Edit task:', task.id)}>
            <Edit className="mr-2 h-3 w-3" />修改任务
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => onOpenReminderConfigModal(task)}>
            <Bell className="mr-2 h-3 w-3" />提醒设置
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => console.log('Task Production Progress:', task.id)}>
            <BarChartHorizontalBig className="mr-2 h-3 w-3" />生产进度
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => console.log('Task Alert Config:', task.id)}>
            <AlertOctagon className="mr-2 h-3 w-3" />报警配置
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem className="text-destructive focus:text-destructive focus:bg-destructive/10" onClick={() => console.log('Delete task:', task.id)}>
            <Trash2 className="mr-2 h-3 w-3" />删除任务
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenuPortal>
    </DropdownMenu>
  );
};

interface VehicleCardContextMenuProps {
  isOpen: boolean;
  position: { x: number; y: number } | null;
  contextData: { vehicle: Vehicle; task: Task } | null;
  onClose: () => void;
  onOpenDeliveryOrderDetailsModal: (vehicle: Vehicle, task: Task) => void;
  onCancelVehicleDispatch: (vehicleId: string) => void;
}

const VehicleCardContextMenu: React.FC<VehicleCardContextMenuProps> = ({
  isOpen,
  position,
  contextData,
  onClose,
  onOpenDeliveryOrderDetailsModal,
  onCancelVehicleDispatch,
}) => {
  if (!isOpen || !position || !contextData) return null;
  const { vehicle, task } = contextData;

  return (
    <DropdownMenu open={isOpen} onOpenChange={onClose}>
      <DropdownMenuTrigger asChild>
        <div style={{ position: 'fixed', left: position.x, top: position.y }} />
      </DropdownMenuTrigger>
      <DropdownMenuPortal>
        <DropdownMenuContent
          sideOffset={5}
          align="start"
          className="z-50 text-xs w-48"
          onCloseAutoFocus={(e) => e.preventDefault()}
        >
          <DropdownMenuItem onClick={() => onOpenDeliveryOrderDetailsModal(vehicle, task)}>
            <Info className="mr-2 h-3 w-3" />查看发货单详情
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => console.log('Copy vehicle info:', vehicle.id)}>
            <Copy className="mr-2 h-3 w-3" />复制车辆信息
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => onCancelVehicleDispatch(vehicle.id)}>
            <Undo2 className="mr-2 h-3 w-3" />取消调度
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => console.log('Forward vehicle to another plant:', vehicle.id)}>
            <Send className="mr-2 h-3 w-3" />转发搅拌站
          </DropdownMenuItem>
          <DropdownMenuSeparator />
           <DropdownMenuItem onClick={() => console.log('Modify production ratio:', vehicle.id)}>
            <SlidersHorizontal className="mr-2 h-3 w-3" />修改生产配比
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => console.log('Request ash splitting:', vehicle.id)}>
            <FileEditIcon className="mr-2 h-3 w-3" />申请分灰
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenuPortal>
    </DropdownMenu>
  );
};

interface TaskListContextMenusProps {
  isTaskContextMenuOpen: boolean;
  taskContextMenuPosition: { x: number; y: number } | null;
  contextMenuTaskData: { taskId: string } | null;
  closeTaskContextMenu: () => void;
  openTankerNoteModal: (task: Task) => void;
  openReminderConfigModal: (task: Task) => void;
  filteredTasks: Task[];

  isVehicleCardContextMenuOpen: boolean;
  vehicleCardContextMenuPosition: { x: number; y: number } | null;
  vehicleCardContextMenuContext: { vehicle: Vehicle; task: Task } | null;
  closeVehicleCardContextMenu: () => void;
  openDeliveryOrderDetailsModal: (vehicle: Vehicle, task: Task) => void;
  cancelVehicleDispatch: (vehicleId: string) => void;
}

export const TaskListContextMenus: React.FC<TaskListContextMenusProps> = ({
  isTaskContextMenuOpen,
  taskContextMenuPosition,
  contextMenuTaskData,
  closeTaskContextMenu,
  openTankerNoteModal,
  openReminderConfigModal,
  filteredTasks,
  isVehicleCardContextMenuOpen,
  vehicleCardContextMenuPosition,
  vehicleCardContextMenuContext,
  closeVehicleCardContextMenu,
  openDeliveryOrderDetailsModal,
  cancelVehicleDispatch,
}) => {
  return (
    <>
      <TaskContextMenu
        isOpen={isTaskContextMenuOpen}
        position={taskContextMenuPosition}
        taskData={contextMenuTaskData}
        onClose={closeTaskContextMenu}
        onOpenTankerNoteModal={openTankerNoteModal}
        onOpenReminderConfigModal={openReminderConfigModal}
        filteredTasks={filteredTasks}
      />
      <VehicleCardContextMenu
        isOpen={isVehicleCardContextMenuOpen}
        position={vehicleCardContextMenuPosition}
        contextData={vehicleCardContextMenuContext}
        onClose={closeVehicleCardContextMenu}
        onOpenDeliveryOrderDetailsModal={openDeliveryOrderDetailsModal}
        onCancelVehicleDispatch={cancelVehicleDispatch}
      />
    </>
  );
};
