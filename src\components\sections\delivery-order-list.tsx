
// src/components/sections/delivery-order-list.tsx
'use client';

import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import dynamic from 'next/dynamic';
import type { DeliveryOrder, DeliveryOrderStatus, Vehicle, Task } from '@/types';
import {
  Table,
  TableBody,
  TableCell,
  TableRow,
} from '@/components/ui/table';
import { CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuPortal,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { Printer, FileSpreadsheet, TruckIcon, MoreVertical, Eye, Briefcase, BarChartHorizontalBig, Send, Undo2, SlidersHorizontal, FileEditIcon, PanelTopClose } from 'lucide-react';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { IconButton } from '@/components/shared/icon-button';
import { cn, getDeliveryOrderStatusIndicatorColor } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { useAppStore } from '@/store/appStore';
import { useUiStore } from '@/store/uiStore';
import { useCurrentPlantInfo } from '@/hooks/useCurrentPlantInfo'; // Import the new hook
import { shallow } from 'zustand/shallow';
import { filterDeliveryOrdersForProductionLine } from '@/services/deliveryOrderService'; 

const DeliveryOrderDetailsModal = dynamic(() =>
  import('@/components/modals/delivery-order-details-modal').then((mod) => mod.DeliveryOrderDetailsModal)
);

interface DeliveryOrderListProps {
  // productionLineCount is removed from props
  onCollapse?: () => void;
  isExpanded?: boolean;
}

export const DeliveryOrderList = React.memo(function DeliveryOrderList({ 
  onCollapse, 
  isExpanded 
}: DeliveryOrderListProps) { // Removed productionLineCount from destructuring
  const storeDeliveryOrders = useAppStore(state => state.deliveryOrders, shallow);
  const selectedPlantId = useUiStore(state => state.selectedPlantId);
  const taskStatusFilter = useUiStore(state => state.taskStatusFilter);
  const { productionLineCount, isLoadingPlants } = useCurrentPlantInfo(); // Use the hook
  const deliveryOrders = Array.isArray(storeDeliveryOrders) ? storeDeliveryOrders : [];
  
  const [isOrderDetailsModalOpen, setIsOrderDetailsModalOpen] = useState(false);
  const [selectedOrderDetail, setSelectedOrderDetail] = useState<DeliveryOrder | null>(null);
  const [contextMenuOpen, setContextMenuOpen] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState<{ x: number; y: number } | null>(null);
  const [contextMenuOrderId, setContextMenuOrderId] = useState<string | null>(null);
  const { toast } = useToast();

  const showDeliveryOrders = taskStatusFilter === 'InProgress';

  const handleOpenDetailsModal = useCallback((order: DeliveryOrder) => {
    setSelectedOrderDetail(order);
    setIsOrderDetailsModalOpen(true);
  }, []);

  const handleContextMenu = useCallback((event: React.MouseEvent, orderId: string) => {
    event.preventDefault();
    event.stopPropagation();
    setContextMenuPosition({ x: event.clientX, y: event.clientY });
    setContextMenuOrderId(orderId);
    setContextMenuOpen(true);
  }, []);

  const handleCloseContextMenu = useCallback(() => {
    setContextMenuOpen(false);
    setContextMenuOrderId(null);
  }, []);

  const handleContextMenuItemClick = useCallback((action: string, orderId: string | null) => {
    if (!orderId) return;
    const order = deliveryOrders.find(o => o.id === orderId);
    toast({
        title: `操作: ${action}`,
        description: `发货单: ${order?.vehicleNumber || orderId}`,
    });
    handleCloseContextMenu();
  }, [deliveryOrders, toast, handleCloseContextMenu]);

  const tableColumns = useMemo(() => [
    { accessorKey: 'vehicleNumber', header: '状态/车号' },
    { accessorKey: 'driver', header: '司机' },
    { accessorKey: 'volume', header: '方量' },
    { accessorKey: 'strength', header: '强度' },
    { accessorKey: 'projectName', header: '工程名称' },
    { accessorKey: 'taskNumber', header: '任务编号' },
  ], []);

  const renderProductionLineOrders = useCallback((lineId: string) => {
    const ordersForLine = filterDeliveryOrdersForProductionLine(deliveryOrders, selectedPlantId, lineId);

    if (ordersForLine.length === 0 && !selectedPlantId && lineId === 'L1' && productionLineCount > 0) {
        return (
             <div className="text-center text-muted-foreground py-4 text-xs w-full flex-1 min-w-[300px]">
                请先选择一个搅拌站。
            </div>
        );
    }
     if (ordersForLine.length === 0 && selectedPlantId) {
         return (
            <div key={lineId} className="flex-1 min-w-[300px] flex-shrink-0 flex flex-col h-full bg-card relative overflow-hidden">
                <CardHeader
                  className="py-0.5 px-1 text-center sticky top-0 z-10 border-b"
                  style={{backgroundColor: 'hsl(var(--block-title-deeper))'}}
                >
                <CardTitle className="text-xs text-center text-foreground">生产线 {lineId} ({ordersForLine.length}车)</CardTitle>
                </CardHeader>
                <div className="flex-1 flex items-center justify-center min-h-0">
                    <CardContent className="p-0">
                        <div className="text-center text-muted-foreground p-4 text-xs">此生产线无发货单。</div>
                    </CardContent>
                </div>
            </div>
        );
    }
     if (ordersForLine.length === 0 && !selectedPlantId) {
        return null;
    }

    return (
      <div key={lineId} className="flex-1 min-w-[300px] flex-shrink-0 flex flex-col h-full bg-card relative overflow-hidden">
        <CardHeader
          className="py-0.5 px-1 text-center sticky top-0 z-10 border-b flex-shrink-0" 
          style={{backgroundColor: 'hsl(var(--block-title-deeper))'}}
        >
          <CardTitle className="text-xs text-center text-foreground">生产线 {lineId} ({ordersForLine.length}车)</CardTitle>
        </CardHeader>
        <div className="flex-1 overflow-y-auto min-h-0"> 
            <CardContent className="p-0"> 
            {ordersForLine.length > 0 ? (
                <Table>
                <TableBody>
                    {ordersForLine.map((order) => (
                    <TableRow
                        key={order.id}
                        className="cursor-pointer text-xs hover:bg-muted/50"
                        onDoubleClick={() => handleOpenDetailsModal(order)}
                        onContextMenu={(e) => handleContextMenu(e, order.id)}
                    >
                        {tableColumns.map((col) => (
                        <TableCell key={col.accessorKey} className="whitespace-nowrap px-1 py-0">
                            {col.accessorKey === 'vehicleNumber' ? (
                            <div className="flex items-center">
                                <div
                                className={cn("w-2.5 h-2.5 rounded-full mr-1.5 flex-shrink-0", getDeliveryOrderStatusIndicatorColor(order.status))}
                                title={order.status ? String(order.status) : '未知状态'}
                                ></div>
                                <TruckIcon className="h-3 w-3 mr-1 text-primary flex-shrink-0" />
                                {order.vehicleNumber}
                            </div>
                            ) : (
                            String(order[col.accessorKey as keyof DeliveryOrder] ?? '')
                            )}
                        </TableCell>
                        ))}
                    </TableRow>
                    ))}
                </TableBody>
                </Table>
            ) : (
                <div className="text-center text-muted-foreground p-4 text-xs">此生产线无发货单。</div>
            )}
            </CardContent>
        </div>
      </div>
    );
  }, [selectedPlantId, deliveryOrders, productionLineCount, tableColumns, handleOpenDetailsModal, handleContextMenu]);

  if (isLoadingPlants) {
    return <div className="flex items-center justify-center h-full">Loading...</div>;
  }

  if (isExpanded === false && showDeliveryOrders) {
    return null; 
  }
  if (!showDeliveryOrders) {
     return null;
  }

  return (
    <div className="flex flex-col h-full overflow-hidden">
      <div className="flex-grow overflow-hidden">
        {selectedPlantId && productionLineCount > 0 ? (
          <ScrollArea className="w-full h-full whitespace-nowrap">
            <div className="flex space-x-0.5 h-full p-0"> 
              {Array.from({ length: productionLineCount }, (_, i) => renderProductionLineOrders(`L${i + 1}`))}
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        ) : (
          <div className="flex items-center justify-center h-full text-muted-foreground text-sm">
            {selectedPlantId ? '此搅拌站无生产线信息。' : '请先选择一个搅拌站。'}
          </div>
        )}
      </div>
      {isOrderDetailsModalOpen && (
        <DeliveryOrderDetailsModal
          isOpen={isOrderDetailsModalOpen}
          onOpenChange={setIsOrderDetailsModalOpen}
          order={selectedOrderDetail}
        />
      )}
      {contextMenuOpen && contextMenuPosition && contextMenuOrderId && (
        <DropdownMenu open={contextMenuOpen} onOpenChange={handleCloseContextMenu}>
            <DropdownMenuTrigger asChild>
                <div style={{ position: 'fixed', left: contextMenuPosition.x, top: contextMenuPosition.y }} />
            </DropdownMenuTrigger>
            <DropdownMenuPortal>
                <DropdownMenuContent
                    sideOffset={5}
                    align="start"
                    className="z-50 text-xs w-40"
                    onCloseAutoFocus={(e) => e.preventDefault()}
                >
                    <DropdownMenuItem onClick={() => {
                        const order = deliveryOrders.find(o => o.id === contextMenuOrderId);
                        if (order) handleOpenDetailsModal(order);
                        handleCloseContextMenu();
                    }}>
                        <Eye className="mr-2 h-3 w-3" />查看详情
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleContextMenuItemClick('打印出货单', contextMenuOrderId)}>
                        <Printer className="mr-2 h-3 w-3" />打印出货单
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleContextMenuItemClick('背砂浆', contextMenuOrderId)}>
                        <Briefcase className="mr-2 h-3 w-3" />背砂浆
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleContextMenuItemClick('单车出货情况', contextMenuOrderId)}>
                        <BarChartHorizontalBig className="mr-2 h-3 w-3" />单车出货情况
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleContextMenuItemClick('转发搅拌站', contextMenuOrderId)}>
                        <Send className="mr-2 h-3 w-3" />转发搅拌站
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleContextMenuItemClick('撤销出车', contextMenuOrderId)}>
                        <Undo2 className="mr-2 h-3 w-3" />撤销出车
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleContextMenuItemClick('修改生产配比', contextMenuOrderId)}>
                        <SlidersHorizontal className="mr-2 h-3 w-3" />修改生产配比
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleContextMenuItemClick('申请分灰', contextMenuOrderId)}>
                        <FileEditIcon className="mr-2 h-3 w-3" />申请分灰
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenuPortal>
        </DropdownMenu>
      )}
    </div>
  );
});
DeliveryOrderList.displayName = 'DeliveryOrderList';
