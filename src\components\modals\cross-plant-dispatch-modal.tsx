
'use client';

import React, { useState } from 'react';
import type { Vehicle, Plant } from '@/types';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
// Input is imported but not used, consider removing if not planned for future use
// import { Input } from '@/components/ui/input'; 
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';

interface CrossPlantDispatchModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  vehicle: Vehicle | null;
  sourcePlant: Plant | null;
  targetPlant: Plant | null;
  onConfirm: (vehicleId: string, targetPlantId: string, notes?: string) => void;
}

export function CrossPlantDispatchModal({
  isOpen,
  onOpenChange,
  vehicle,
  sourcePlant,
  targetPlant,
  onConfirm,
}: CrossPlantDispatchModalProps) {
  const [notes, setNotes] = useState('');
  const { toast } = useToast();

  if (!vehicle || !sourcePlant || !targetPlant) return null;

  const handleConfirm = () => {
    onConfirm(vehicle.id, targetPlant.id, notes);
    toast({
      title: '跨站调度已提交',
      description: `车辆 ${vehicle.vehicleNumber} 从 ${sourcePlant.name} 调度到 ${targetPlant.name}. 备注: ${notes || '无'}`,
    });
    onOpenChange(false);
    setNotes('');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>车辆跨站调度确认</DialogTitle>
          <DialogDescription>
            请确认将车辆从 {sourcePlant.name} 调度到 {targetPlant.name}。
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">调度车辆:</span>
            <span>{vehicle.vehicleNumber} (ID: {vehicle.id})</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">原属搅拌站:</span>
            <span>{sourcePlant.name}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">目标搅拌站:</span>
            <span>{targetPlant.name}</span>
          </div>
          <div className="grid w-full gap-1.5">
            <Label htmlFor="cross-dispatch-notes">调度备注 (可选)</Label>
            <Textarea
              placeholder="输入调度备注..."
              id="cross-dispatch-notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="min-h-[60px]"
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button type="button" onClick={handleConfirm}>
            确认调度
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
