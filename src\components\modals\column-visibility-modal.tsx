
'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import type { CustomColumnDefinition } from '@/types';
import { GripVertical, ArrowUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ColumnVisibilityModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  allColumns: CustomColumnDefinition[]; // These are the reorderable column definitions
  columnVisibility: Record<string, boolean>;
  onColumnVisibilityChange: (columnId: string, checked: boolean) => void;
  currentOrder: string[]; // Array of column IDs in their current display order
  onOrderChange: (newOrder: string[]) => void;
}

export function ColumnVisibilityModal({
  isOpen,
  onOpenChange,
  allColumns, // Definitions of reorderable columns
  columnVisibility,
  onColumnVisibilityChange,
  currentOrder,
  onOrderChange,
}: ColumnVisibilityModalProps) {
  const [draggedItemId, setDraggedItemId] = useState<string | null>(null);
  const [dragOverItemId, setDragOverItemId] = useState<string | null>(null);

  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, columnId: string) => {
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', columnId);
    setDraggedItemId(columnId);
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>, columnId: string) => {
    e.preventDefault();
    if (draggedItemId && draggedItemId !== columnId) {
      setDragOverItemId(columnId);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault(); // Necessary to allow dropping
    if (draggedItemId) {
      e.dataTransfer.dropEffect = 'move';
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    // Only clear if leaving the item itself, not its children
     if (e.currentTarget === e.target || !e.currentTarget.contains(e.relatedTarget as Node)) {
        setDragOverItemId(null);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>, targetColumnId: string) => {
    e.preventDefault();
    if (!draggedItemId || draggedItemId === targetColumnId) {
      setDraggedItemId(null);
      setDragOverItemId(null);
      return;
    }

    const newOrder = [...currentOrder];
    const draggedIndex = newOrder.indexOf(draggedItemId);
    let targetIndex = newOrder.indexOf(targetColumnId);

    if (draggedIndex === -1) { // Should not happen if drag started correctly
        setDraggedItemId(null);
        setDragOverItemId(null);
        return;
    }

    // Remove the dragged item
    const [movedItem] = newOrder.splice(draggedIndex, 1);

    // Re-calculate targetIndex after removal if necessary
    if(targetIndex > draggedIndex) {
        targetIndex--; // Adjust target index if it was after the dragged item
    }

    // Insert at the new position
    newOrder.splice(targetIndex, 0, movedItem);

    onOrderChange(newOrder);
    setDraggedItemId(null);
    setDragOverItemId(null);
  };

  const handleDragEnd = () => {
    setDraggedItemId(null);
    setDragOverItemId(null);
  };


  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>列显示与排序设置</DialogTitle>
          <DialogDescription>
            勾选以显示列，使用排序按钮调整列顺序。部分关键列为必显示项。
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="max-h-[60vh] py-4 pr-2">
          <div className="space-y-4">
            {currentOrder.map((columnId) => {
              const colDef = allColumns.find(col => col.id === columnId);
              if (!colDef) return null;
              
              return (
                <div key={columnId} className="space-y-2">
                  {(() => {
                    const isMandatory = colDef.isMandatory;
                    const isDraggingThis = draggedItemId === colDef.id;
                    const isDragOverThis = dragOverItemId === colDef.id;

                    return (
                      <div
                        draggable={!isMandatory}
                        onDragStart={!isMandatory ? (e) => handleDragStart(e, colDef.id as string) : undefined}
                        onDragEnter={!isMandatory ? (e) => handleDragEnter(e, colDef.id as string) : undefined}
                        onDragOver={!isMandatory ? handleDragOver : undefined}
                        onDragLeave={!isMandatory ? handleDragLeave : undefined}
                        onDrop={!isMandatory ? (e) => handleDrop(e, colDef.id as string) : undefined}
                        onDragEnd={!isMandatory ? handleDragEnd : undefined}
                        className={cn(
                          "flex items-center space-x-2 p-2 border rounded-md transition-all",
                          !isMandatory && "hover:bg-muted/80",
                          isDraggingThis ? "opacity-50 bg-primary/20" : "bg-card",
                          isDragOverThis && draggedItemId && draggedItemId !== colDef.id ? "ring-2 ring-primary ring-offset-1" : "",
                          !isMandatory && draggedItemId ? "cursor-grabbing" : (!isMandatory ? "cursor-grab" : "cursor-default")
                        )}
                      >
                        {!isMandatory && (
                          <GripVertical className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                        )}
                        {isMandatory && (
                           <div className="w-4 h-4 flex-shrink-0"></div> // Placeholder for alignment
                        )}
                        <Checkbox
                          id={`vis-${colDef.id}`}
                          checked={columnVisibility[colDef.id as string] ?? (colDef.isMandatory || colDef.defaultVisible)}
                          onCheckedChange={(checked) => {
                            if (!isMandatory) {
                              onColumnVisibilityChange(colDef.id as string, !!checked);
                            }
                          }}
                          disabled={isMandatory}
                          className="flex-shrink-0"
                        />
                        <Label
                          htmlFor={`vis-${colDef.id}`}
                          className={cn(
                              "flex-grow min-w-0 truncate",
                              isMandatory ? 'text-muted-foreground cursor-not-allowed' : 'cursor-pointer'
                          )}
                          title={colDef.label}
                        >
                          {colDef.label} {isMandatory && "(必选)"}
                        </Label>
                      </div>
                    );
                  })()
                  }
                </div>
              );
            })}
          </div>
        </ScrollArea>
        <DialogFooter>
          <Button type="button" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
