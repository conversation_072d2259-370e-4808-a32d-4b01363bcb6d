
'use client'

import React from 'react';
import dynamic from 'next/dynamic';
import {
  Play, Pause, CheckCircle, XCircle, Send, Edit, ListChecks, BarChartBig, Settings, Users, LogOut, Combine, Truck, Printer, BookCopy, Percent, Mic,
} from 'lucide-react';
import { IconButton } from '@/components/shared/icon-button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'; 
import { taskStatusOptions, productTypeOptions, defaultVolumeOptions } from '@/data/mock-data';
// appStore is not directly needed here for plants anymore, as plants are passed via props
import { useUiStore } from '@/store/uiStore';
import type { Plant } from '@/types'; // Import Plant type
import { useTaskSelectionState } from '@/contexts/TaskSelectionContext';

const AnnounceVehicleArrivalModal = dynamic(() =>
  import('@/components/modals/announce-vehicle-arrival-modal').then((mod) => mod.AnnounceVehicleArrivalModal)
);

function ActionGroup({ children }: { children: React.ReactNode }) {
  return <div className="flex items-center gap-0.5 p-0.5 bg-background rounded-md border">{children}</div>;
}

interface ActionBarProps {
  plants: Plant[]; // Add plants prop
}

function ActionBar({ plants }: ActionBarProps) { // Destructure plants from props
  const { taskStatusFilter, setTaskStatusFilter } = useUiStore();
  const { selectedTask, selectedTaskId } = useTaskSelectionState();

  // 检查是否有选中的任务
  const hasSelectedTask = !!selectedTaskId;
  const selectedTaskStatus = selectedTask?.dispatchStatus;
  
  return (
    <div className="flex items-center space-x-0.5 flex-wrap gap-0.5">
      {/* 选中任务状态指示器 */}
      {hasSelectedTask ? (
        <ActionGroup>
          <div className="px-3 py-1 text-xs bg-primary text-primary-foreground rounded border flex items-center gap-2">
            <div className="w-2 h-2 bg-primary-foreground rounded-full"></div>
            已选中: {selectedTask?.taskNumber}
            <span className="text-primary-foreground/70">({selectedTask?.dispatchStatus})</span>
          </div>
        </ActionGroup>
      ) : (
        <ActionGroup>
          <div className="px-3 py-1 text-xs bg-muted text-muted-foreground rounded border flex items-center gap-2">
            <div className="w-2 h-2 bg-muted-foreground/50 rounded-full"></div>
            未选中任务
          </div>
        </ActionGroup>
      )}

      <ActionGroup>
        <Select 
          value={taskStatusFilter} 
          onValueChange={setTaskStatusFilter} 
        >
          <SelectTrigger className="w-[110px] h-7 text-xs px-1.5">
            <SelectValue placeholder="任务状态过滤" />
          </SelectTrigger>
          <SelectContent>
            {taskStatusOptions.map(opt => (
              <SelectItem key={opt.value} value={opt.value} className="text-xs">{opt.label}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </ActionGroup>

      <ActionGroup>
        <IconButton
          icon={Play}
          tooltipLabel={hasSelectedTask ? `准备生产 - ${selectedTask?.taskNumber}` : "准备生产 (请先选择任务)"}
          onClick={() => hasSelectedTask && console.log('Prepare Production', selectedTaskId)}
          disabled={!hasSelectedTask}
        />
        <IconButton
          icon={Play}
          tooltipLabel={hasSelectedTask ? `转到正在进行 - ${selectedTask?.taskNumber}` : "转到正在进行 (请先选择任务)"}
          onClick={() => hasSelectedTask && console.log('Set to In Progress', selectedTaskId)}
          disabled={!hasSelectedTask}
        />
        <IconButton
          icon={Pause}
          tooltipLabel={hasSelectedTask ? `暂停任务 - ${selectedTask?.taskNumber}` : "暂停任务 (请先选择任务)"}
          onClick={() => hasSelectedTask && console.log('Pause Task', selectedTaskId)}
          disabled={!hasSelectedTask || selectedTaskStatus !== 'InProgress'}
        />
        <IconButton
          icon={CheckCircle}
          tooltipLabel={hasSelectedTask ? `完成任务 - ${selectedTask?.taskNumber}` : "完成任务 (请先选择任务)"}
          onClick={() => hasSelectedTask && console.log('Complete Task', selectedTaskId)}
          disabled={!hasSelectedTask || selectedTaskStatus !== 'InProgress'}
        />
        <IconButton
          icon={XCircle}
          tooltipLabel={hasSelectedTask ? `撤销任务 - ${selectedTask?.taskNumber}` : "撤销任务 (请先选择任务)"}
          onClick={() => hasSelectedTask && console.log('Cancel Task', selectedTaskId)}
          disabled={!hasSelectedTask}
        />
      </ActionGroup>

      <ActionGroup>
        <IconButton
          icon={Send}
          tooltipLabel={hasSelectedTask ? `发送生产指令 - ${selectedTask?.taskNumber}` : "发送生产指令 (请先选择任务)"}
          onClick={() => hasSelectedTask && console.log('Send Production Instruction', selectedTaskId)}
          disabled={!hasSelectedTask || selectedTaskStatus !== 'InProgress'}
        />
        <IconButton
          icon={Truck}
          tooltipLabel={hasSelectedTask ? `安排泵车 - ${selectedTask?.taskNumber}` : "安排泵车 (请先选择任务)"}
          onClick={() => hasSelectedTask && console.log('Assign Pump Truck', selectedTaskId)}
          disabled={!hasSelectedTask}
        />
        <IconButton
          icon={Combine}
          tooltipLabel={hasSelectedTask ? `安排其他车辆 - ${selectedTask?.taskNumber}` : "安排其他车辆 (请先选择任务)"}
          onClick={() => hasSelectedTask && console.log('Assign Other Vehicle', selectedTaskId)}
          disabled={!hasSelectedTask}
        />
      </ActionGroup>

      <ActionGroup>
        <IconButton
          icon={Edit}
          tooltipLabel={hasSelectedTask ? `修改任务 - ${selectedTask?.taskNumber}` : "修改任务 (请先选择任务)"}
          onClick={() => hasSelectedTask && console.log('Edit Task', selectedTaskId)}
          disabled={!hasSelectedTask}
        />
        <IconButton
          icon={ListChecks}
          tooltipLabel={hasSelectedTask ? `发车明细 - ${selectedTask?.taskNumber}` : "发车明细 (请先选择任务)"}
          onClick={() => hasSelectedTask && console.log('Dispatch Details', selectedTaskId)}
          disabled={!hasSelectedTask}
        />
        <IconButton
          icon={BarChartBig}
          tooltipLabel={hasSelectedTask ? `生产进度 - ${selectedTask?.taskNumber}` : "生产进度 (请先选择任务)"}
          onClick={() => hasSelectedTask && console.log('Production Progress', selectedTaskId)}
          disabled={!hasSelectedTask}
        />
      </ActionGroup>
      
      <ActionGroup>
         {/* Use plants from props */}
         {plants && plants.length > 0 && <AnnounceVehicleArrivalModal plants={plants} />} 
        <IconButton icon={Settings} tooltipLabel="系统参数设置" onClick={() => console.log('System Settings')} />
      </ActionGroup>

      <ActionGroup>
        <IconButton icon={Users} tooltipLabel="换班" onClick={() => console.log('Shift Change')} />
        <IconButton icon={BookCopy} tooltipLabel="交接班记录" onClick={() => console.log('Shift Log')} />
      </ActionGroup>

      <ActionGroup>
        <IconButton icon={Printer} tooltipLabel="罐车出车统计" onClick={() => console.log('Tanker Dispatch Stats')} />
        <IconButton icon={BarChartBig} tooltipLabel="调度工程统计" onClick={() => console.log('Project Dispatch Stats')} />
        <IconButton icon={Truck} tooltipLabel="泵车出车统计" onClick={() => console.log('Pump Truck Dispatch Stats')} />
      </ActionGroup>

      <ActionGroup>
         <Select defaultValue="Normal">
          <SelectTrigger className="w-[100px] h-7 text-xs px-1.5">
            <SelectValue placeholder="缺省方量" />
          </SelectTrigger>
          <SelectContent>
            {defaultVolumeOptions.map(opt => (
              <SelectItem key={opt.value} value={opt.value} className="text-xs">{opt.label}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select defaultValue="All">
          <SelectTrigger className="w-[110px] h-7 text-xs px-1.5">
            <SelectValue placeholder="产品种类过滤" />
          </SelectTrigger>
          <SelectContent>
            {productTypeOptions.map(opt => (
              <SelectItem key={opt.value} value={opt.value} className="text-xs">{opt.label}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </ActionGroup>
    </div>
  );
}

const MemoizedActionBar = React.memo(ActionBar);
export { MemoizedActionBar as ActionBar };
