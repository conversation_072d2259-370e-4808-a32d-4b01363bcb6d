
// src/components/sections/task-list/task-strength-badge.tsx
'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface TaskStrengthBadgeProps {
  strength: string;
  heightClass: string;
  textSizeClass: string;
  paddingXClass: string;
  textStyleClass?: string; // Optional for additional text styling like color from column config
  baseBgClass?: string; // Optional for base background if not using textStyleClass for bg
}

export const TaskStrengthBadge = React.memo(function TaskStrengthBadge({
  strength,
  heightClass,
  textSizeClass,
  paddingXClass,
  textStyleClass,
  baseBgClass = 'bg-muted text-muted-foreground', // Default background and text
}: TaskStrengthBadgeProps) {
  return (
    <span
      className={cn(
        "py-0 rounded shadow-sm flex items-center justify-center",
        heightClass,
        textSizeClass,
        paddingXClass,
        textStyleClass ? '' : baseBgClass, // Apply baseBgClass only if textStyleClass is not providing its own bg
        textStyleClass // This might include color, which would override text-muted-foreground
      )}
      title={`强度: ${strength}`}
    >
      {strength}
    </span>
  );
});

TaskStrengthBadge.displayName = 'TaskStrengthBadge';
