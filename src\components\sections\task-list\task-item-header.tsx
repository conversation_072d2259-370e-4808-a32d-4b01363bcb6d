
// src/components/sections/task-list/task-item-header.tsx
'use client';

import React from 'react';
import type {Task, TaskListDensityMode} from '@/types';
import { RingProgress } from '@/components/shared/ring-progress';
import { cn } from '@/lib/utils';
import { TaskStatusBadge } from './cards/TaskStatusBadge';
import { TaskStrengthBadge } from './task-strength-badge';
import { TaskItemDetailsRow } from './task-item-details-row'; // New Import
import { CheckCircle2 } from 'lucide-react';

interface TaskItemHeaderProps {
    task: Task;
    getStatusLabelProps: (status?: Task['dispatchStatus']) => { label: string; className: string };
    density: Exclude<TaskListDensityMode, 'card' | ''>;
    isSelected?: boolean;
}

export const TaskItemHeader = React.memo(function TaskItemHeader({
    task,
    getStatusLabelProps,
    density,
    isSelected = false
}: TaskItemHeaderProps) {
    const progress = task.requiredVolume > 0 ? ((task.completedVolume / task.requiredVolume) * 100 ).toFixed(0) + '%' : '0%';

    let headerPaddingClass = 'py-1';
    let ringProgressRadius = 9;
    let ringProgressStrokeWidth = 2.5;
    let baseTextSize = 'text-[9px]';
    let projectNameTextSize = 'text-[11px]';
    let statusBadgeHeight = 'h-[18px]';
    let statusBadgeTextSize = 'text-[9px]';
    let statusBadgePaddingX = 'px-1';
    let iconSizeClasses = 'h-[10px] w-[10px]';
    let detailSpacing = 'space-x-1';

    if (density === 'compact') {
        headerPaddingClass = 'py-0.5';
        ringProgressRadius = 7;
        ringProgressStrokeWidth = 2;
        baseTextSize = 'text-[8px]';
        projectNameTextSize = 'text-[10px]';
        statusBadgeHeight = 'h-4';
        statusBadgeTextSize = 'text-[8px]';
        statusBadgePaddingX = 'px-0.5';
        iconSizeClasses = 'h-2 w-2';
        detailSpacing = 'space-x-0.5';
    } else if (density === 'loose') {
        headerPaddingClass = 'py-1.5';
        ringProgressRadius = 11;
        ringProgressStrokeWidth = 3;
        baseTextSize = 'text-[10px]';
        projectNameTextSize = 'text-xs';
        statusBadgeHeight = 'h-5';
        statusBadgeTextSize = 'text-[10px]';
        statusBadgePaddingX = 'px-1.5';
        iconSizeClasses = 'h-2.5 w-2.5';
        detailSpacing = 'space-x-1.5';
    }

    const showRingProgress = density !== 'compact';
    const iconClasses = cn(iconSizeClasses, "mr-0.5 flex-shrink-0 text-muted-foreground");
    const baseDetailClasses = cn("truncate inline-flex items-center text-foreground", baseTextSize);
    const completedTaskStyle = task.dispatchStatus === 'Completed' ? { backgroundColor: 'hsl(var(--status-success-bg))', color: 'hsl(var(--status-success-fg))' } : undefined;

    return (
        <div className={cn(
            "flex items-center justify-between w-full relative overflow-hidden",
            headerPaddingClass
        )}>
            <div className={cn(
                "flex items-center flex-1 min-w-0 overflow-hidden whitespace-nowrap",
                showRingProgress ? "pl-0.5 space-x-1" : "pl-0 space-x-0.5"
            )}>
                {showRingProgress && (
                    <RingProgress
                        value={task.requiredVolume > 0 ? (task.completedVolume / task.requiredVolume) * 100 : 0}
                        radius={ringProgressRadius}
                        strokeWidth={ringProgressStrokeWidth}
                        label={progress}
                        className="flex-shrink-0 hidden sm:flex mr-1"
                    />
                )}
                <TaskItemDetailsRow
                    task={task}
                    projectNameTextSize={projectNameTextSize}
                    baseTextSize={baseTextSize}
                    iconClasses={iconClasses}
                    baseDetailClasses={baseDetailClasses}
                    detailSpacing={detailSpacing}
                />
            </div>

            <div className="flex items-center space-x-1 flex-shrink-0 ml-1">
                {isSelected && (
                    <CheckCircle2 className={cn(
                        "text-accent flex-shrink-0",
                        density === 'compact' ? "w-3 h-3" : "w-4 h-4"
                    )} />
                )}
                <TaskStrengthBadge
                  strength={task.strength}
                  heightClass={statusBadgeHeight}
                  textSizeClass={statusBadgeTextSize}
                  paddingXClass={statusBadgePaddingX}
                  baseBgClass="bg-muted text-muted-foreground"
                />
                <TaskStatusBadge
                  status={task.dispatchStatus}
                  {...getStatusLabelProps(task.dispatchStatus)}
                  className={cn(statusBadgeHeight)}
                  textSizeClass={statusBadgeTextSize as 'small' | 'medium' | 'large' | 'extra-large'}
                  paddingXClass={statusBadgePaddingX as 'px-2' | 'px-3' | 'px-4'}
                  completedTaskStyle={completedTaskStyle}
                />
            </div>
        </div>
    );
});
TaskItemHeader.displayName = 'TaskItemHeader';
