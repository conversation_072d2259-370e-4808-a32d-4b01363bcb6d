// src/ai/flows/announce-vehicle-arrivals.ts
'use server';

/**
 * @fileOverview Announces vehicle arrival information using a Genkit flow and text-to-speech.
 *
 * - announceVehicleArrival - A function to announce vehicle arrival details.
 * - AnnounceVehicleArrivalInput - The input type for the announceVehicleArrival function.
 * - AnnounceVehicleArrivalOutput - The return type for the announceVehicleArrival function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const AnnounceVehicleArrivalInputSchema = z.object({
  mixingPlant: z.string().describe('The name of the mixing plant.'),
  vehicleNumber: z.string().describe('The vehicle number.'),
  repeatCount: z.number().describe('The number of times to repeat the announcement.'),
  speechRate: z.number().optional().describe('The speech rate for the announcement (optional).'),
  language: z.string().optional().describe('The language for the announcement (optional).'),
  accent: z.string().optional().describe('The accent for the announcement (optional).'),
});

export type AnnounceVehicleArrivalInput = z.infer<typeof AnnounceVehicleArrivalInputSchema>;

const AnnounceVehicleArrivalOutputSchema = z.object({
  announcement: z.string().describe('The generated announcement message.'),
});

export type AnnounceVehicleArrivalOutput = z.infer<typeof AnnounceVehicleArrivalOutputSchema>;

export async function announceVehicleArrival(input: AnnounceVehicleArrivalInput): Promise<AnnounceVehicleArrivalOutput> {
  return announceVehicleArrivalFlow(input);
}

const announceVehicleArrivalPrompt = ai.definePrompt({
  name: 'announceVehicleArrivalPrompt',
  input: {schema: AnnounceVehicleArrivalInputSchema},
  output: {schema: AnnounceVehicleArrivalOutputSchema},
  prompt: `Announce the arrival of vehicle {{vehicleNumber}} at mixing plant {{mixingPlant}}. Repeat the announcement {{repeatCount}} times. 

    Consider using a {{language}} language and a {{accent}} accent. Adjust the speech rate to {{speechRate}}.`,
});

const announceVehicleArrivalFlow = ai.defineFlow(
  {
    name: 'announceVehicleArrivalFlow',
    inputSchema: AnnounceVehicleArrivalInputSchema,
    outputSchema: AnnounceVehicleArrivalOutputSchema,
  },
  async input => {
    let announcementText = '';
    for (let i = 0; i < input.repeatCount; i++) {
      const {output} = await announceVehicleArrivalPrompt(input);
      announcementText += output!.announcement + ' ';
    }

    // Here, we would ideally integrate with a text-to-speech service to actually
    // make the announcement audible.  This placeholder shows how the announcement
    // text would be generated.

    return {announcement: announcementText};
  }
);
