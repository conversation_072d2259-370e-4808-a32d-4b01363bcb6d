'use client';

import { useState } from 'react';
import { VehicleCardBackgroundSettings, getVehicleCardBackgroundStyle } from '@/components/modals/vehicle-card-background-settings';
import type { InTaskVehicleCardStyle } from '@/types';

export default function TestVehicleBackgroundPage() {
  const [styles, setStyles] = useState<InTaskVehicleCardStyle>({
    cardWidth: 'w-16',
    cardHeight: 'h-8',
    fontSize: 'text-[10px]',
    fontColor: 'text-foreground',
    vehicleNumberFontWeight: 'font-medium',
    cardBgColor: 'bg-card/80',
    statusDotSize: 'w-1 h-1',
    borderRadius: 'rounded-md',
    boxShadow: 'shadow-sm',
    vehiclesPerRow: 4,
    gradientEnabled: false,
    gradientDirection: 'to-r',
    gradientStartColor: '#3b82f6',
    gradientEndColor: '#8b5cf6',
    cardGradient: undefined,
  });

  const backgroundStyle = getVehicleCardBackgroundStyle(styles);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">车辆卡片背景色设置测试</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 设置面板 */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">背景设置</h2>
          <div className="border rounded-lg p-4">
            <VehicleCardBackgroundSettings
              currentStyles={styles}
              onStylesChange={setStyles}
            />
          </div>
        </div>

        {/* 预览区域 */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">预览效果</h2>
          
          {/* 单个卡片预览 */}
          <div className="space-y-2">
            <h3 className="text-md font-medium">单个卡片</h3>
            <div 
              className={`w-16 h-8 border rounded-md flex items-center justify-center text-xs font-medium ${backgroundStyle.className}`}
              style={backgroundStyle.style}
            >
              车001
            </div>
          </div>

          {/* 多个卡片预览 */}
          <div className="space-y-2">
            <h3 className="text-md font-medium">多个卡片</h3>
            <div className="grid grid-cols-4 gap-2">
              {Array.from({ length: 8 }, (_, i) => (
                <div 
                  key={i}
                  className={`w-16 h-8 border rounded-md flex items-center justify-center text-xs font-medium ${backgroundStyle.className}`}
                  style={backgroundStyle.style}
                >
                  车{String(i + 1).padStart(3, '0')}
                </div>
              ))}
            </div>
          </div>

          {/* 不同状态的卡片预览 */}
          <div className="space-y-2">
            <h3 className="text-md font-medium">不同状态</h3>
            <div className="grid grid-cols-2 gap-2">
              {/* 正常状态 */}
              <div 
                className={`w-16 h-8 border rounded-md flex items-center justify-center text-xs font-medium ${backgroundStyle.className}`}
                style={backgroundStyle.style}
              >
                正常
              </div>
              
              {/* 洗泵水状态 */}
              <div className="w-16 h-8 border rounded-md flex items-center justify-center text-xs font-medium bg-red-400 dark:bg-red-500/90">
                洗泵水
              </div>
              
              {/* 暂停状态 */}
              <div className="w-16 h-8 border rounded-md flex items-center justify-center text-xs font-medium bg-pink-100/90 dark:bg-pink-900/50">
                暂停
              </div>
              
              {/* 停用状态 */}
              <div className="w-16 h-8 border rounded-md flex items-center justify-center text-xs font-medium bg-pink-100/90 dark:bg-pink-900/50">
                停用
              </div>
            </div>
          </div>

          {/* 当前设置信息 */}
          <div className="space-y-2">
            <h3 className="text-md font-medium">当前设置</h3>
            <div className="text-sm space-y-1 bg-muted p-3 rounded">
              <div>背景色: {styles.cardBgColor}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
