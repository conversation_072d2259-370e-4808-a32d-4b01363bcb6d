// src/components/ui/dispatch-countdown.tsx
'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { differenceInSeconds } from 'date-fns';
import { Clock, AlertTriangle, CheckCircle, Pause } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface DispatchCountdownProps {
  /** 下次计划发车时间 */
  nextScheduledTime?: string | Date | null;
  /** 上次发车时间 */
  lastDispatchTime?: string | Date | null;
  /** 发车频率（分钟） */
  dispatchFrequencyMinutes?: number;
  /** 发车状态 */
  dispatchStatus?: 'New' | 'ReadyToProduce' | 'RatioSet' | 'InProgress' | 'Paused' | 'Completed' | 'Cancelled';
  /** 自定义样式类名 */
  className?: string;
  /** 紧凑模式 */
  compact?: boolean;
  /** 显示图标 */
  showIcon?: boolean;
}

type CountdownState = 'idle' | 'pending' | 'warning' | 'urgent' | 'overdue' | 'completed';

export const DispatchCountdown: React.FC<DispatchCountdownProps> = ({
  nextScheduledTime,
  lastDispatchTime,
  dispatchFrequencyMinutes,
  dispatchStatus = 'New',
  className,
  compact = false,
  showIcon = true,
}) => {
  const [timeDisplay, setTimeDisplay] = useState<string>('-');
  const [state, setState] = useState<CountdownState>('idle');
  const [progress, setProgress] = useState<number>(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 格式化时间显示
  const formatTime = useCallback((totalSeconds: number): string => {
    const absSeconds = Math.abs(totalSeconds);
    
    const days = Math.floor(absSeconds / (24 * 3600));
    const hours = Math.floor((absSeconds % (24 * 3600)) / 3600);
    const minutes = Math.floor((absSeconds % 3600) / 60);
    const seconds = absSeconds % 60;

    if (days > 0) {
      return compact ? `${days}d ${hours}h` : `${days}天 ${hours}小时`;
    } else if (hours > 0) {
      return compact ? `${hours}h ${minutes}m` : `${hours}小时 ${minutes}分`;
    } else if (minutes > 0) {
      return compact ? `${minutes}m ${seconds}s` : `${minutes}分 ${seconds}秒`;
    } else {
      return compact ? `${seconds}s` : `${seconds}秒`;
    }
  }, [compact]);

  // 更新倒计时
  const updateCountdown = useCallback(() => {
    const now = new Date();

    // 如果没有设置发车频率
    if (!dispatchFrequencyMinutes || dispatchFrequencyMinutes <= 0) {
      if (!lastDispatchTime) {
        setTimeDisplay('-');
        setState('idle');
        setProgress(0);
      } else {
        const lastTime = new Date(lastDispatchTime);
        const secondsSince = differenceInSeconds(now, lastTime);
        setTimeDisplay(formatTime(secondsSince));
        setState('completed');
        setProgress(100);
      }
      return;
    }

    // 如果没有下次计划时间
    if (!nextScheduledTime) {
      setTimeDisplay('-');
      setState('idle');
      setProgress(0);
      return;
    }

    const nextTime = new Date(nextScheduledTime);
    const totalSecondsDiff = differenceInSeconds(nextTime, now);
    const totalFrequencySeconds = dispatchFrequencyMinutes * 60;

    if (totalSecondsDiff < 0) {
      // 已超时
      const overdueSeconds = Math.abs(totalSecondsDiff);
      setTimeDisplay(`+${formatTime(overdueSeconds)}`);
      setState('overdue');
      setProgress(100);
    } else {
      // 倒计时中
      setTimeDisplay(formatTime(totalSecondsDiff));
      
      // 计算进度
      const elapsedSeconds = totalFrequencySeconds - totalSecondsDiff;
      const progressPercent = Math.min(100, (elapsedSeconds / totalFrequencySeconds) * 100);
      setProgress(progressPercent);

      // 设置状态
      if (totalSecondsDiff <= 60) {
        setState('urgent');
      } else if (totalSecondsDiff <= 300) { // 5分钟
        setState('warning');
      } else {
        setState('pending');
      }
    }
  }, [nextScheduledTime, lastDispatchTime, dispatchFrequencyMinutes, formatTime]);

  useEffect(() => {
    updateCountdown();
    
    // 设置定时器
    const interval = setInterval(updateCountdown, 1000);
    timerRef.current = interval;

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [updateCountdown]);

  // 获取状态配置
  const getStateConfig = (currentState: CountdownState) => {
    switch (currentState) {
      case 'idle':
        return {
          icon: Pause,
          color: 'text-gray-500',
          bgColor: 'bg-gray-100 dark:bg-gray-800',
          borderColor: 'border-gray-200 dark:border-gray-700',
          progressColor: 'bg-gray-300',
          label: '待定',
        };
      case 'pending':
        return {
          icon: Clock,
          color: 'text-blue-600 dark:text-blue-400',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-800',
          progressColor: 'bg-blue-500',
          label: '等待中',
        };
      case 'warning':
        return {
          icon: Clock,
          color: 'text-amber-600 dark:text-amber-400',
          bgColor: 'bg-amber-50 dark:bg-amber-900/20',
          borderColor: 'border-amber-200 dark:border-amber-800',
          progressColor: 'bg-amber-500',
          label: '即将发车',
        };
      case 'urgent':
        return {
          icon: AlertTriangle,
          color: 'text-orange-600 dark:text-orange-400',
          bgColor: 'bg-orange-50 dark:bg-orange-900/20',
          borderColor: 'border-orange-200 dark:border-orange-800',
          progressColor: 'bg-orange-500',
          label: '紧急',
        };
      case 'overdue':
        return {
          icon: AlertTriangle,
          color: 'text-red-600 dark:text-red-400',
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          borderColor: 'border-red-200 dark:border-red-800',
          progressColor: 'bg-red-500',
          label: '超时',
        };
      case 'completed':
        return {
          icon: CheckCircle,
          color: 'text-green-600 dark:text-green-400',
          bgColor: 'bg-green-50 dark:bg-green-900/20',
          borderColor: 'border-green-200 dark:border-green-800',
          progressColor: 'bg-green-500',
          label: '已完成',
        };
      default:
        return {
          icon: Clock,
          color: 'text-gray-500',
          bgColor: 'bg-gray-100',
          borderColor: 'border-gray-200',
          progressColor: 'bg-gray-300',
          label: '未知',
        };
    }
  };

  const config = getStateConfig(state);
  const IconComponent = config.icon;

  // 获取工具提示内容
  const getTooltipContent = () => {
    const nextTime = nextScheduledTime ? new Date(nextScheduledTime).toLocaleString('zh-CN') : '未计划';
    const lastTime = lastDispatchTime ? new Date(lastDispatchTime).toLocaleString('zh-CN') : '从未';
    const freq = dispatchFrequencyMinutes ? `${dispatchFrequencyMinutes}分钟` : '未设置';
    
    return (
      <div className="space-y-2 text-sm">
        <div className="font-medium text-center">{config.label}</div>
        <div className="space-y-1 text-xs">
          <div><span className="text-muted-foreground">状态:</span> {config.label}</div>
          <div><span className="text-muted-foreground">显示:</span> {timeDisplay}</div>
          <div><span className="text-muted-foreground">下次发车:</span> {nextTime}</div>
          <div><span className="text-muted-foreground">上次发车:</span> {lastTime}</div>
          <div><span className="text-muted-foreground">发车频率:</span> {freq}</div>
          {progress > 0 && (
            <div><span className="text-muted-foreground">进度:</span> {Math.round(progress)}%</div>
          )}
        </div>
      </div>
    );
  };

  if (compact) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className={cn(
              "inline-flex items-center gap-1 px-2 py-1 rounded-md border text-xs font-medium transition-colors cursor-help",
              config.bgColor,
              config.borderColor,
              config.color,
              className
            )}>
              {showIcon && <IconComponent className="w-3 h-3" />}
              <span>{timeDisplay}</span>
            </div>
          </TooltipTrigger>
          <TooltipContent side="top" className="max-w-xs">
            {getTooltipContent()}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={cn(
            "relative overflow-hidden rounded-lg border transition-all duration-200 cursor-help hover:shadow-md",
            config.bgColor,
            config.borderColor,
            className
          )}>
            {/* 进度条背景 */}
            {progress > 0 && (
              <div className="absolute inset-0 opacity-10">
                <div 
                  className={cn("h-full transition-all duration-1000", config.progressColor)}
                  style={{ width: `${progress}%` }}
                />
              </div>
            )}
            
            {/* 内容 */}
            <div className="relative flex items-center justify-between">
              <div className="flex items-center gap-2">
                {showIcon && (
                  <IconComponent className={cn("w-4 h-4", config.color)} />
                )}
                <div>
                  <div className={cn("font-medium", config.color)}>
                    {timeDisplay}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {config.label}
                  </div>
                </div>
              </div>
              
              {/* 进度指示器 */}
              {progress > 0 && (
                <div className="text-right">
                  <div className="text-xs text-muted-foreground">
                    {Math.round(progress)}%
                  </div>
                  <div className="w-12 h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    <div 
                      className={cn("h-full transition-all duration-1000", config.progressColor)}
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-xs">
          {getTooltipContent()}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
