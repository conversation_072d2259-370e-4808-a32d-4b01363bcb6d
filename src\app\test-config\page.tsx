'use client';

import { useEffect, useState } from 'react';
import type { TaskListStoredSettings } from '@/types';

export default function TestConfigPage() {
  const [config, setConfig] = useState<TaskListStoredSettings | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadConfig = () => {
      try {
        const stored = localStorage.getItem('taskListSettings_v4.0');
        if (stored) {
          const loadedConfig = JSON.parse(stored);
          setConfig(loadedConfig);
        }
      } catch (error) {
        console.error('Failed to load config:', error);
      } finally {
        setLoading(false);
      }
    };

    loadConfig();
  }, []);

  const handleForceReload = () => {
    setLoading(true);
    try {
      localStorage.removeItem('taskListSettings_v4.0');
      localStorage.removeItem('taskCardConfig_v2.0');
      localStorage.removeItem('uiConfigVersion');
      setConfig(null);
    } catch (error) {
      console.error('Failed to force reload:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="p-8">Loading configuration...</div>;
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">配置测试页面</h1>
      
      <div className="mb-4">
        <button 
          onClick={handleForceReload}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          强制重新加载默认配置
        </button>
      </div>

      <div className="space-y-6">
        <div>
          <h2 className="text-lg font-semibold mb-2">基本设置</h2>
          <div className="bg-gray-50 p-4 rounded">
            <p><strong>显示模式:</strong> {config?.displayMode}</p>
            <p><strong>密度:</strong> {config?.density}</p>
            <p><strong>斑马条纹:</strong> {config?.enableZebraStriping ? '启用' : '禁用'}</p>
          </div>
        </div>

        <div>
          <h2 className="text-lg font-semibold mb-2">列文本样式</h2>
          <div className="bg-gray-50 p-4 rounded">
            <pre className="text-sm overflow-auto">
              {JSON.stringify(config?.columnTextStyles, null, 2)}
            </pre>
          </div>
        </div>

        <div>
          <h2 className="text-lg font-semibold mb-2">列背景色</h2>
          <div className="bg-gray-50 p-4 rounded">
            <pre className="text-sm overflow-auto">
              {JSON.stringify(config?.columnBackgrounds, null, 2)}
            </pre>
          </div>
        </div>

        <div>
          <h2 className="text-lg font-semibold mb-2">车辆卡片样式</h2>
          <div className="bg-gray-50 p-4 rounded">
            <pre className="text-sm overflow-auto">
              {JSON.stringify(config?.inTaskVehicleCardStyles, null, 2)}
            </pre>
          </div>
        </div>

        <div>
          <h2 className="text-lg font-semibold mb-2">完整配置</h2>
          <div className="bg-gray-50 p-4 rounded max-h-96 overflow-auto">
            <pre className="text-xs">
              {JSON.stringify(config, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
