# 任务列表样式配置指南

## 概述

我已经完全重写了 `ListItemStyleConfigModal` 组件，实现了您要求的所有功能：

- ✅ **布局配置**：调整item高度、内边距、间距、圆角等
- ✅ **模块样式**：配置每个模块的背景色和显示隐藏
- ✅ **字段样式**：设置每个字段的字号、字重、颜色
- ✅ **调度车辆宽度**：可调整调度车辆区域宽度
- ✅ **字段归属**：管理字段所属的模块
- ✅ **显示隐藏**：控制字段和模块的显示状态

## 新的配置界面

### 四个主要标签页

#### 1. 📐 布局设置
- **整体布局**
  - 行高：紧凑(40px) / 正常(50px) / 宽松(60px) / 很宽松(70px)
  - 内边距：小(4px) / 正常(8px) / 大(12px) / 很大(16px)
  - 元素间距：小(4px) / 正常(8px) / 大(12px) / 很大(16px)
  - 圆角：无圆角 / 小圆角 / 正常圆角 / 大圆角

- **调度车辆区域**
  - 区域宽度：32-80 (对应 8rem-20rem)
  - 滑块调节，实时预览

#### 2. 🎨 模块样式
每个模块都可以独立配置：

**可配置模块：**
- 📋 顶部状态栏 (header)
- ⭐ 最重要信息 (critical)
- 📝 普通信息 (normal)
- 🚛 调度车辆 (dispatch)
- 🏭 生产线 (production)

**每个模块的配置项：**
- 背景颜色：透明/白色/浅灰/蓝色浅/绿色浅/红色浅等
- 显示隐藏：开关控制
- 预览效果：实时查看样式效果
- 特殊配置：调度车辆模块可调整宽度

#### 3. 🔤 字段样式
为每个字段单独配置文本样式：

**可配置属性：**
- **字体大小**：极小(8px) / 很小(10px) / 小(12px) / 正常(14px) / 中等(16px) / 大(18px)
- **字体粗细**：细 / 正常 / 中等 / 粗 / 很粗
- **文字颜色**：默认 / 主色 / 蓝色 / 绿色 / 红色 / 橙色 / 紫色 / 灰色 / 辅助色

**实时预览：**
每个字段都有预览区域，可以立即看到样式效果

#### 4. 👁️ 显示设置
管理字段的显示隐藏和排序：

**功能特性：**
- 按组管理字段（最重要信息/基础信息/时间信息/其他信息/固定位置）
- 拖拽排序字段
- 批量显示/隐藏整组字段
- 单个字段显示/隐藏控制
- 视觉指示器（眼睛图标）

## 配置数据结构

### 字段样式配置
```typescript
interface FieldStyle {
  fontSize: string;     // 'text-xs', 'text-sm', 'text-base' 等
  fontWeight: string;   // 'font-normal', 'font-medium', 'font-bold' 等
  textColor: string;    // 'text-foreground', 'text-blue-600' 等
}
```

### 模块样式配置
```typescript
interface ModuleStyle {
  backgroundColor: string;  // 'bg-transparent', 'bg-blue-50' 等
  visible: boolean;        // 模块是否显示
  width?: string;          // 调度车辆模块的宽度 'w-48' 等
}
```

### 布局配置
```typescript
interface LayoutConfig {
  itemHeight: string;      // 'min-h-[55px]' 等
  itemPadding: string;     // 'p-2' 等
  itemGap: string;         // 'gap-2' 等
  borderRadius: string;    // 'rounded-lg' 等
}
```

## 使用方法

### 1. 打开配置界面
点击任务列表右上角的设置按钮（齿轮图标）

### 2. 调整布局
- 在"布局设置"标签页调整整体布局参数
- 使用滑块调整调度车辆区域宽度

### 3. 配置模块样式
- 在"模块样式"标签页为每个模块设置背景色
- 使用开关控制模块显示隐藏
- 查看实时预览效果

### 4. 设置字段样式
- 在"字段样式"标签页为每个字段设置文本样式
- 选择合适的字体大小、粗细和颜色
- 利用预览功能确认效果

### 5. 管理显示设置
- 在"显示设置"标签页控制字段显示隐藏
- 拖拽调整字段顺序
- 批量管理字段组

### 6. 保存配置
点击"保存"按钮应用所有更改

## 默认配置

系统提供了合理的默认配置：

```typescript
// 默认模块样式
{
  header: { backgroundColor: 'bg-transparent', visible: true },
  critical: { backgroundColor: 'bg-accent/20', visible: true },
  normal: { backgroundColor: 'bg-transparent', visible: true },
  dispatch: { backgroundColor: 'bg-blue-50/50', visible: true, width: 'w-48' },
  production: { backgroundColor: 'bg-green-50/50', visible: true },
}

// 默认布局配置
{
  itemHeight: 'min-h-[55px]',
  itemPadding: 'p-2',
  itemGap: 'gap-2',
  borderRadius: 'rounded-lg',
}
```

## 特色功能

### 🎯 智能预览
- 每个配置项都有实时预览
- 模块样式预览显示背景色效果
- 字段样式预览显示文本效果

### 🔧 灵活配置
- 调度车辆区域宽度可调（解决宽度不够问题）
- 每个字段都可以独立设置样式
- 模块可以独立显示隐藏

### 💾 配置持久化
- 所有配置自动保存到设置中
- 重新加载页面后配置保持不变
- 支持导入导出配置（未来功能）

### 🎨 视觉优化
- 清晰的标签页分类
- 直观的图标和标识
- 友好的用户界面

## 技术实现

### 组件架构
- 使用 Tabs 组件分类管理不同配置
- Select 组件提供预设选项
- Slider 组件支持数值调节
- Switch 组件控制开关状态

### 样式系统
- 基于 Tailwind CSS 的样式类
- 动态样式应用
- cn() 函数合并样式类

### 状态管理
- React useState 管理配置状态
- useCallback 优化性能
- 实时同步到父组件

这个新的配置系统为任务列表提供了极大的灵活性，用户可以根据自己的需求完全自定义界面样式和布局。
