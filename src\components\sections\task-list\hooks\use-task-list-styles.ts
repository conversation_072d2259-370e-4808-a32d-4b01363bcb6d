// src/components/sections/task-list/hooks/use-task-list-styles.ts

import { useMemo, useCallback } from 'react';
import type { Task, StyleableColumnId } from '@/types';
import { getDensityStyles, getCardGridClasses } from '../task-list-density-config';
import { getStyleableColumnId } from '../task-list-type-guards';
import { allBackgroundColorOptionsMap } from '@/components/modals/column-specific-style-modal';
import { useTaskListSettings } from '@/hooks/useTaskListSettings';
import { useUiStore } from '@/store/uiStore';

/**
 * 任务列表样式计算Hook
 * 负责计算各种样式相关的值和函数
 */
export function useTaskListStyles() {
  const { settings } = useTaskListSettings();
  const { vehicleDisplayMode } = useUiStore();

  // 当前密度样式
  const currentDensityStyles = useMemo(() => {
    return getDensityStyles(settings.density);
  }, [settings.density]);

  // 卡片网格容器样式
  const cardGridContainerClasses = useMemo(() => {
    return getCardGridClasses(settings.density);
  }, [settings.density]);

  // 行高估算函数
  const estimateRowHeight = useCallback((task?: Task): number => {
    const baseHeight = settings.density === 'compact' ? 36 : settings.density === 'loose' ? 46 : 42;
    if (!task || vehicleDisplayMode !== 'internalId' || !task.vehicles || task.vehicles.length === 0) {
      return baseHeight;
    }

    const vehiclesPerRow = settings.inTaskVehicleCardStyles.vehiclesPerRow || 3;
    const vehicleCardHeight = settings.inTaskVehicleCardStyles.cardSize === 'small' ? 60 : 80;
    const gap = settings.inTaskVehicleCardStyles.gap || 8;
    
    const numRows = Math.ceil(task.vehicles.length / vehiclesPerRow);
    const vehiclesHeight = numRows * vehicleCardHeight + (numRows - 1) * gap;

    // Add some padding
    return baseHeight + vehiclesHeight + 16;
  }, [settings.density, vehicleDisplayMode, settings.inTaskVehicleCardStyles]);

  // 列背景属性获取函数
  const getColumnBackgroundProps = useCallback((columnId: string, isHeader: boolean, isFixed: boolean): { style: React.CSSProperties, className: string } => {
    const safeColumnId = getStyleableColumnId(columnId);
    const bgSetting = settings.columnBackgrounds[safeColumnId];
    const bgOption = bgSetting ? allBackgroundColorOptionsMap.get(bgSetting) : null;
    let style: React.CSSProperties = {};
    let className = '';

    if (bgOption) {
      if (isFixed && bgOption.specificSolidColor) {
        style.backgroundColor = bgOption.specificSolidColor;
      } else if (bgOption.themeClassName) {
        className = bgOption.themeClassName;
      } else if (bgOption.specificSolidColor) {
        style.backgroundColor = bgOption.specificSolidColor;
      }
    } else if (isFixed) {
      // 为固定列设置默认的不透明背景色
      const stickyStyle = settings.tableStyleConfig?.stickyColumnStyle;
      if (stickyStyle?.backgroundColor) {
        // 如果配置中有背景色，优先使用配置的值
        if (stickyStyle.backgroundColor.startsWith('bg-')) {
          // 如果是Tailwind类名，添加到className
          className = stickyStyle.backgroundColor;
        } else {
          // 如果是具体的颜色值，设置到style
          style.backgroundColor = stickyStyle.backgroundColor;
        }
      } else {
        // 默认背景色
        className = 'bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60';
      }
    }

    return { style, className };
  }, [settings.columnBackgrounds, settings.tableStyleConfig]);

  // 表格总宽度计算
  const tableTotalWidth = useMemo(() => {
    return Object.values(settings.columnWidths).reduce((sum, width) => sum + width, 0);
  }, [settings.columnWidths]);

  // 状态标签属性获取函数
  const getStatusLabelProps = useCallback((status?: Task['dispatchStatus']) => {
    switch (status) {
      case 'New': 
        return { label: '新任务', className: 'bg-blue-100 text-blue-700 dark:bg-blue-700 dark:text-blue-100' };
      case 'ReadyToProduce': 
        return { label: '准备生产', className: 'bg-cyan-100 text-cyan-700 dark:bg-cyan-700 dark:text-cyan-100' };
      case 'RatioSet': 
        return { label: '已设定配比', className: 'bg-purple-100 text-purple-700 dark:bg-purple-700 dark:text-purple-100' };
      case 'InProgress': 
        return { label: '正在进行', className: 'bg-teal-500 text-white' };
      case 'Paused': 
        return { label: '暂停', className: 'bg-orange-400 text-white' };
      case 'Completed': 
        return { label: '已完成', className: 'bg-status-success-bg text-status-success-fg' };
      case 'Cancelled': 
        return { label: '已撤销', className: 'bg-red-500 text-white' };
      default: 
        return { label: status || '未知', className: 'bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-200' };
    }
  }, []);

  // 主题相关样式
  const themeStyles = useMemo(() => {
    return {
      // 表格样式
      table: {
        headerBg: 'bg-muted/50',
        rowHover: 'hover:bg-muted/50',
        rowSelected: 'bg-accent/50',
        border: 'border-border',
      },
      // 卡片样式
      card: {
        bg: 'bg-card',
        border: 'border-border',
        shadow: 'shadow-sm',
        hover: 'hover:shadow-md',
      },
      // 拖拽样式
      drag: {
        over: 'bg-primary/10 ring-2 ring-primary',
        placeholder: 'bg-muted/30 border-2 border-dashed border-muted-foreground/30',
      },
    };
  }, []);

  // 响应式样式
  const responsiveStyles = useMemo(() => {
    return {
      // 移动端适配
      mobile: {
        padding: 'p-2',
        margin: 'm-1',
        fontSize: 'text-sm',
      },
      // 桌面端
      desktop: {
        padding: 'p-4',
        margin: 'm-2',
        fontSize: 'text-base',
      },
    };
  }, []);

  return {
    // 密度相关
    currentDensityStyles,
    cardGridContainerClasses,
    estimateRowHeight,
    
    // 列样式
    getColumnBackgroundProps,
    tableTotalWidth,
    
    // 状态样式
    getStatusLabelProps,
    
    // 主题样式
    themeStyles,
    responsiveStyles,
    
    // 便捷方法
    getDensityClass: (property: keyof typeof currentDensityStyles) => currentDensityStyles[property],
    isCompactMode: settings.density === 'compact',
    isLooseMode: settings.density === 'loose',
    isNormalMode: settings.density === 'normal',
  };
}
