// src/components/sections/task-list/hooks/use-task-list-data.ts

import { useMemo } from 'react';
import { shallow } from 'zustand/shallow';
import type { Task, Vehicle, TaskGroup } from '@/types';
import { useAppStore } from '@/store/appStore';
import { useFilteredTasks } from '@/hooks/useFilteredTasks';
import { groupTasks } from '@/utils/task-grouping';
import { useTaskListSettings } from '@/hooks/useTaskListSettings';

/**
 * 任务列表数据处理Hook
 * 负责处理任务数据的获取、过滤、分组等逻辑
 */
export function useTaskListData() {
  // 基础数据
  const allTasks = useAppStore(state => state.tasks, shallow);
  const allVehicles = useAppStore(state => state.vehicles, shallow);
  const filteredTasks = useFilteredTasks();
  const { settings } = useTaskListSettings();

  // 优化：预先计算每个任务的车辆列表
  const tasksWithVehicles = useMemo(() => {
    const vehiclesByTask = new Map<string, Vehicle[]>();
    allVehicles.forEach(vehicle => {
      if (vehicle.assignedTaskId) {
        if (!vehiclesByTask.has(vehicle.assignedTaskId)) {
          vehiclesByTask.set(vehicle.assignedTaskId, []);
        }
        vehiclesByTask.get(vehicle.assignedTaskId)!.push(vehicle);
      }
    });
    return filteredTasks.map(task => ({ 
      ...task, 
      vehicles: vehiclesByTask.get(task.id) || [] 
    }));
  }, [filteredTasks, allVehicles]);

  // 分组数据计算 - 优化依赖项以减少重新计算
  const taskGroups = useMemo(() => {
    return groupTasks(tasksWithVehicles, settings.groupConfig);
  }, [
    tasksWithVehicles, 
    settings.groupConfig.enabled,
    settings.groupConfig.groupBy,
    settings.groupConfig.defaultCollapsed,
    settings.groupConfig.sortOrder
  ]);

  // 展开的任务列表（用于表格显示）
  const expandedTasks = useMemo(() => {
    if (!settings.groupConfig.enabled) {
      return tasksWithVehicles;
    }
    return taskGroups
      .filter(group => !group.collapsed)
      .flatMap(group => group.tasks);
  }, [taskGroups, tasksWithVehicles, settings.groupConfig.enabled]);

  // 分组统计
  const groupStats = useMemo(() => {
    if (!settings.groupConfig.enabled) {
      return null;
    }
    
    return {
      totalGroups: taskGroups.length,
      collapsedGroups: taskGroups.filter(g => g.collapsed).length,
      expandedGroups: taskGroups.filter(g => !g.collapsed).length,
      totalTasks: taskGroups.reduce((sum, g) => sum + g.tasks.length, 0),
      visibleTasks: expandedTasks.length,
    };
  }, [taskGroups, expandedTasks, settings.groupConfig.enabled]);

  // 车辆统计
  const vehicleStats = useMemo(() => {
    const assignedVehicles = allVehicles.filter(v => v.assignedTaskId);
    const availableVehicles = allVehicles.filter(v => !v.assignedTaskId);
    
    return {
      total: allVehicles.length,
      assigned: assignedVehicles.length,
      available: availableVehicles.length,
      assignedVehicles,
      availableVehicles,
    };
  }, [allVehicles]);

  // 任务统计
  const taskStats = useMemo(() => {
    const stats = {
      total: filteredTasks.length,
      new: 0,
      readyToProduce: 0,
      ratioSet: 0,
      inProgress: 0,
      paused: 0,
      completed: 0,
      cancelled: 0,
    };

    filteredTasks.forEach(task => {
      switch (task.dispatchStatus) {
        case 'New':
          stats.new++;
          break;
        case 'ReadyToProduce':
          stats.readyToProduce++;
          break;
        case 'RatioSet':
          stats.ratioSet++;
          break;
        case 'InProgress':
          stats.inProgress++;
          break;
        case 'Paused':
          stats.paused++;
          break;
        case 'Completed':
          stats.completed++;
          break;
        case 'Cancelled':
          stats.cancelled++;
          break;
      }
    });

    return stats;
  }, [filteredTasks]);

  return {
    // 原始数据
    allTasks,
    allVehicles,
    filteredTasks,
    
    // 处理后的数据
    tasksWithVehicles,
    taskGroups,
    expandedTasks,
    
    // 统计数据
    groupStats,
    vehicleStats,
    taskStats,
    
    // 便捷方法
    getTaskById: (id: string) => allTasks.find(t => t.id === id),
    getVehicleById: (id: string) => allVehicles.find(v => v.id === id),
    getTaskVehicles: (taskId: string) => allVehicles.filter(v => v.assignedTaskId === taskId),
  };
}
