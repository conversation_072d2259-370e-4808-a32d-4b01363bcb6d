import { getApiConfig, buildUrl } from '@/config/api';

// HTTP客户端错误类型
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// 请求配置接口
export interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, string>;
  timeout?: number;
}

// HTTP客户端类
class HttpClient {
  private config = getApiConfig();

  // 通用请求方法
  async request<T>(endpoint: string, config: RequestConfig = {}): Promise<T> {
    const {
      method = 'GET',
      headers = {},
      body,
      params,
      timeout = this.config.timeout,
    } = config;

    // 构建URL
    const url = buildUrl(endpoint, params);
    
    // Mock数据拦截 - 如果URL为空，说明是mock模式
    if (url === '') {
      return this.handleMockRequest<T>(endpoint, config);
    }

    // 设置默认headers
    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...headers,
    };

    // 构建fetch配置
    const fetchConfig: RequestInit = {
      method,
      headers: defaultHeaders,
      signal: AbortSignal.timeout(timeout),
    };

    // 添加请求体
    if (body && method !== 'GET') {
      fetchConfig.body = JSON.stringify(body);
    }

    try {
      const response = await fetch(url, fetchConfig);

      if (!response.ok) {
        throw new ApiError(
          `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          await response.text()
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      // 处理网络错误、超时等
      throw new ApiError(
        `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        0
      );
    }
  }

  // Mock数据处理
  private async handleMockRequest<T>(endpoint: string, _config: RequestConfig): Promise<T> {
    // 动态导入mock数据，避免在生产环境加载
    const { 
      generateMockTasks, 
      generateMockVehicles, 
      generateMockDeliveryOrders, 
      mockPlants 
    } = await import('@/data/mock-data');

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 50));

    // 根据端点返回对应的mock数据
    switch (endpoint) {
      case '/tasks':
        return generateMockTasks() as T;
      case '/vehicles':
        return generateMockVehicles() as T;
      case '/delivery-orders':
        return generateMockDeliveryOrders() as T;
      case '/plants':
        return mockPlants as T;
      default:
        throw new ApiError(`Mock endpoint not found: ${endpoint}`, 404);
    }
  }

  // 便捷方法
  get<T>(endpoint: string, params?: Record<string, string>): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET', params });
  }

  post<T>(endpoint: string, body?: any): Promise<T> {
    return this.request<T>(endpoint, { method: 'POST', body });
  }

  put<T>(endpoint: string, body?: any): Promise<T> {
    return this.request<T>(endpoint, { method: 'PUT', body });
  }

  delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  patch<T>(endpoint: string, body?: any): Promise<T> {
    return this.request<T>(endpoint, { method: 'PATCH', body });
  }
}

// 导出单例实例
export const httpClient = new HttpClient();
