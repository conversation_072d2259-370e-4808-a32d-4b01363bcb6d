
'use client';

import React, { useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel,
} from '@/components/ui/select';
import type { CustomColumnDefinition, ColumnTextStyle } from '@/types';
import { cn } from '@/lib/utils';

interface BackgroundColorOption {
  label: string;
  value: string; // Unique value for the select item
  specificSolidColor?: string | null; // Direct HSL/hex color for solid fill (used for SVG)
  themeClassName?: string | null; // For theme-based colors (semi-transparent or solid) - Tailwind class
  previewClassName: string; // For the preview swatch in the modal
}

// "实底背景" - Solid, Opaque, Light Colors (primarily for fixed columns via SVG, or non-fixed via inline style)
const solidColorOptions: BackgroundColorOption[] = [
  { label: '默认 (列固有)', value: 'default-solid', specificSolidColor: 'hsl(220, 10%, 92%)', themeClassName: null, previewClassName: 'bg-slate-600 dark:bg-slate-600' },
  { label: '白色', value: 'white', specificSolidColor: 'hsl(0, 0%, 100%)', themeClassName: null, previewClassName: 'bg-white border' },
  { label: '浅灰', value: 'light-gray', specificSolidColor: 'hsl(220, 10%, 92%)', themeClassName: null, previewClassName: 'bg-slate-600 dark:bg-slate-700' },
  { label: '淡粉', value: 'pale-pink', specificSolidColor: 'hsl(340, 60%, 93%)', themeClassName: null, previewClassName: 'bg-pink-100 dark:bg-pink-800' },
  { label: '淡蓝', value: 'pale-blue', specificSolidColor: 'hsl(200, 60%, 93%)', themeClassName: null, previewClassName: 'bg-sky-100 dark:bg-sky-800' },
  { label: '淡绿', value: 'pale-green', specificSolidColor: 'hsl(130, 40%, 93%)', themeClassName: null, previewClassName: 'bg-emerald-100 dark:bg-emerald-800' },
  { label: '淡黄', value: 'pale-yellow', specificSolidColor: 'hsl(50, 70%, 93%)', themeClassName: null, previewClassName: 'bg-yellow-100 dark:bg-yellow-800' },
  { label: '淡紫', value: 'pale-purple', specificSolidColor: 'hsl(270, 50%, 94%)', themeClassName: null, previewClassName: 'bg-purple-100 dark:bg-purple-800' },
  { label: '浅米', value: 'light-beige', specificSolidColor: 'hsl(40, 30%, 92%)', themeClassName: null, previewClassName: 'bg-orange-100 dark:bg-orange-800' },
  { label: '淡青', value: 'pale-cyan', specificSolidColor: 'hsl(175, 50%, 92%)', themeClassName: null, previewClassName: 'bg-cyan-100 dark:bg-cyan-800' },
  { label: '冰蓝色', value: 'ice-blue-solid', specificSolidColor: 'hsl(190, 60%, 94%)', themeClassName: null, previewClassName: 'bg-blue-100 dark:bg-blue-900' },
  { label: '薄荷青', value: 'mint-aqua-solid', specificSolidColor: 'hsl(165, 55%, 93%)', themeClassName: null, previewClassName: 'bg-green-100 dark:bg-green-900' }, // Using green as a proxy for aqua
  { label: '冷灰色', value: 'cool-gray-solid', specificSolidColor: 'hsl(210, 25%, 94%)', themeClassName: null, previewClassName: 'bg-slate-100 dark:bg-slate-800' },
];

// "浅底背景" - Light, Semi-Transparent Colors (primarily for non-fixed columns via Tailwind classes)
const semiTransparentColorOptions: BackgroundColorOption[] = [
  { label: '默认 (透明)', value: 'default-semi', specificSolidColor: null, themeClassName: null, previewClassName: 'bg-transparent border-dashed border-gray-400' },
  { label: '柔和灰浅底 (15%)', value: 'muted-light-15', specificSolidColor: null, themeClassName: 'bg-muted/15', previewClassName: 'bg-muted/15' },
  { label: '主题色浅底 (15%)', value: 'primary-light-15', specificSolidColor: null, themeClassName: 'bg-primary/15', previewClassName: 'bg-primary/15' },
  { label: '强调色浅底 (15%)', value: 'accent-light-15', specificSolidColor: null, themeClassName: 'bg-accent/15', previewClassName: 'bg-accent/15' },
  { label: '警示色浅底 (15%)', value: 'destructive-light-15', specificSolidColor: null, themeClassName: 'bg-destructive/15', previewClassName: 'bg-destructive/15' },
  { label: '信息蓝浅底 (15%)', value: 'info-light-15', specificSolidColor: null, themeClassName: 'bg-sky-500/15 dark:bg-sky-600/15', previewClassName: 'bg-sky-500/15 dark:bg-sky-600/15' },
  { label: '成功绿浅底 (15%)', value: 'success-light-15', specificSolidColor: null, themeClassName: 'bg-green-500/15 dark:bg-green-600/15', previewClassName: 'bg-green-500/15 dark:bg-green-600/15' },
  { label: '警告黄浅底 (15%)', value: 'warning-light-15', specificSolidColor: null, themeClassName: 'bg-yellow-500/15 dark:bg-yellow-600/15', previewClassName: 'bg-yellow-500/15 dark:bg-yellow-600/15' },
  { label: '青色浅底 (15%)', value: 'cyan-light-15', specificSolidColor: null, themeClassName: 'bg-cyan-500/15 dark:bg-cyan-600/15', previewClassName: 'bg-cyan-500/15 dark:bg-cyan-600/15' },
  { label: '蓝灰浅底 (15%)', value: 'slate-light-15', specificSolidColor: null, themeClassName: 'bg-slate-500/15 dark:bg-slate-600/15', previewClassName: 'bg-slate-500/15 dark:bg-slate-600/15' },
  { label: '水鸭浅底 (15%)', value: 'teal-light-15', specificSolidColor: null, themeClassName: 'bg-teal-500/15 dark:bg-teal-600/15', previewClassName: 'bg-teal-500/15 dark:bg-teal-600/15' },
];

export const allBackgroundColorOptions = [...solidColorOptions, ...semiTransparentColorOptions];
export const allBackgroundColorOptionsMap = new Map(allBackgroundColorOptions.map(opt => [opt.value, opt]));


interface TextColorOption {
  label: string;
  value: string; // Unique value for the select item, this is what gets stored
  className: string; // Tailwind class name
  previewClass: string; // Tailwind class for color swatch
}

const textColorGroups: { label: string; options: TextColorOption[] }[] = [
  {
    label: '主题相关',
    options: [
      { label: '默认 (前景)', value: 'default', className: 'text-foreground', previewClass: 'bg-foreground' },
      { label: '主要 (主题)', value: 'primary', className: 'text-primary', previewClass: 'bg-primary' },
      { label: '强调 (主题)', value: 'accent', className: 'text-accent', previewClass: 'bg-accent' },
      { label: '柔和 (主题)', value: 'muted', className: 'text-muted-foreground', previewClass: 'bg-muted-foreground' },
      { label: '警示 (主题)', value: 'destructive', className: 'text-destructive', previewClass: 'bg-destructive' },
    ]
  },
  {
    label: '中性色调',
    options: [
      { label: '深灰', value: 'dark-gray', className: 'text-gray-700 dark:text-gray-300', previewClass: 'bg-gray-700' },
      { label: '中灰', value: 'medium-gray', className: 'text-gray-500 dark:text-gray-400', previewClass: 'bg-gray-500' },
      { label: '浅灰', value: 'light-gray-text', className: 'text-gray-400 dark:text-gray-500', previewClass: 'bg-gray-400' }, // Renamed value to avoid conflict
      { label: '白色', value: 'white-text', className: 'text-white', previewClass: 'bg-white border' },
      { label: '黑色', value: 'black-text', className: 'text-black', previewClass: 'bg-black' },
    ]
  },
  {
    label: '鲜艳/柔和色调',
    options: [
      { label: '石灰绿', value: 'lime', className: 'text-lime-600 dark:text-lime-400', previewClass: 'bg-lime-600' },
      { label: '青色', value: 'teal', className: 'text-teal-600 dark:text-teal-400', previewClass: 'bg-teal-600' },
      { label: '天蓝色', value: 'sky-blue-text', className: 'text-sky-600 dark:text-sky-400', previewClass: 'bg-sky-600' }, // Renamed value
      { label: '靛蓝', value: 'indigo', className: 'text-indigo-600 dark:text-indigo-400', previewClass: 'bg-indigo-600' },
      { label: '紫色', value: 'purple-text', className: 'text-purple-600 dark:text-purple-400', previewClass: 'bg-purple-600' }, // Renamed value
      { label: '粉色', value: 'pink-text', className: 'text-pink-600 dark:text-pink-400', previewClass: 'bg-pink-600' }, // Renamed value
      { label: '玫瑰红', value: 'rose', className: 'text-rose-600 dark:text-rose-400', previewClass: 'bg-rose-600' },
      { label: '琥珀黄', value: 'amber', className: 'text-amber-600 dark:text-amber-400', previewClass: 'bg-amber-600' },
    ]
  }
];
export const allTextColorOptions = textColorGroups.flatMap(group => group.options);
export const allTextColorOptionsMap = new Map(allTextColorOptions.map(opt => [opt.value, opt]));


const fontSizes = [
  { label: '默认', value: 'default' },
  { label: '小 (12px)', value: 'text-[12px]' },
  { label: '中 (13px)', value: 'text-[13px]' },
  { label: '大 (14px)', value: 'text-[14px]' },
  { label: '较大 (15px)', value: 'text-[15px]' },
  { label: '特大 (16px)', value: 'text-[16px]' },
];

const fontWeights = [
  { label: '默认', value: 'default' },
  { label: '正常 (400)', value: 'font-normal' },
  { label: '中等 (500)', value: 'font-medium' },
  { label: '半粗 (600)', value: 'font-semibold' },
  { label: '粗体 (700)', value: 'font-bold' },
];

interface ColumnSpecificStyleModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  columnDef: CustomColumnDefinition | null;
  currentTextStyles: ColumnTextStyle | undefined;
  currentBackgroundSetting: string | undefined; // This will store the 'value' from BackgroundColorOption
  onTextStyleChange: (property: keyof ColumnTextStyle, value: string) => void;
  onBackgroundColorChange: (valueToStore: string) => void; // Callback now expects the 'value'
}

export function ColumnSpecificStyleModal({
  isOpen,
  onOpenChange,
  columnDef,
  currentTextStyles,
  currentBackgroundSetting, // This is the 'value' (e.g., 'white', 'primary-light-5')
  onTextStyleChange,
  onBackgroundColorChange,
}: ColumnSpecificStyleModalProps) {
  if (!columnDef) return null;

  const handleBgChange = (selectedValue: string) => {
    onBackgroundColorChange(selectedValue);
  };

  const currentBgOptionValue = useMemo(() => {
    let value = columnDef.fixed ? 'default-solid' : 'default-semi';
    if (currentBackgroundSetting && allBackgroundColorOptionsMap.has(currentBackgroundSetting)) {
      value = currentBackgroundSetting;
    }
    return value;
  }, [columnDef.fixed, currentBackgroundSetting]);

  const handleTextColorChange = (value: string) => {
    onTextStyleChange('color', value);
  };

  const currentTextColorValue = useMemo(() => {
    return currentTextStyles?.color || 'default';
  }, [currentTextStyles?.color]);

  const currentFontSizeValue = useMemo(() => {
    return currentTextStyles?.fontSize || 'default';
  }, [currentTextStyles?.fontSize]);

  const currentFontWeightValue = useMemo(() => {
    return currentTextStyles?.fontWeight || 'default';
  }, [currentTextStyles?.fontWeight]);

  const showTextStyles = columnDef.isStyleable && columnDef.id !== 'dispatchedVehicles' && columnDef.id !== 'productionLines';

  let descriptionText = `自定义 “${columnDef.label}” 列的外观。`;
  if (columnDef.id === 'dispatchedVehicles' || columnDef.id === 'productionLines') {
    descriptionText = `自定义 “${columnDef.label}” 列的背景外观。固定列背景总是实底。`;
  }


  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>设置列样式: {columnDef.label}</DialogTitle>
          <DialogDescription>
            {descriptionText}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-6 py-4">
          <div>
            <Label htmlFor={`bgColor-${columnDef.id}`} className="text-sm font-medium mb-2 block">
              列背景色
            </Label>
            <Select
              key={`bg-color-${columnDef.id}-${currentBgOptionValue}`}
              value={currentBgOptionValue}
              onValueChange={handleBgChange}
            >
              <SelectTrigger id={`bgColor-${columnDef.id}`}>
                <SelectValue placeholder="选择背景色" />
              </SelectTrigger>
              <SelectContent>
                {columnDef.fixed ? (
                <SelectGroup>
                <SelectLabel>实底背景 (推荐固定列)</SelectLabel>
                {solidColorOptions.map((opt) => (
                  <SelectItem key={opt.value} value={opt.value}>
                    <span className="flex items-center">
                      <span
                        className={cn("w-3 h-3 rounded-sm mr-2 border", opt.previewClassName)}
                        style={opt.specificSolidColor && !opt.previewClassName.includes('bg-') ? { backgroundColor: opt.specificSolidColor } : {}}
                      ></span>
                      {opt.label}
                    </span>
                  </SelectItem>
                ))}
              </SelectGroup>
              ) :(
              <SelectGroup>
                <SelectLabel>浅底背景 (推荐普通列)</SelectLabel>
                {semiTransparentColorOptions.map((opt) => (
                  <SelectItem key={opt.value} value={opt.value}>
                    <span className="flex items-center">
                      <span
                        className={cn("w-3 h-3 rounded-sm mr-2 border", opt.previewClassName)}
                      ></span>
                      {opt.label}
                    </span>
                  </SelectItem>
                ))}
              </SelectGroup> 
              )}
                
                
              </SelectContent>
            </Select>
          </div>

          {showTextStyles && (
            <>
              <div>
                <Label htmlFor={`textColor-${columnDef.id}`} className="text-sm font-medium mb-2 block">
                  文本颜色
                </Label>
                <Select
                  key={`text-color-${columnDef.id}-${currentTextColorValue}`}
                  value={currentTextColorValue}
                  onValueChange={handleTextColorChange}
                >
                  <SelectTrigger id={`textColor-${columnDef.id}`}>
                    <SelectValue placeholder="选择颜色" />
                  </SelectTrigger>
                  <SelectContent>
                    {textColorGroups.map((group, index) => (
                      <SelectGroup key={`${group.label}-${index}`}>
                        <SelectLabel>{group.label}</SelectLabel>
                        {group.options.map((opt) => (
                          <SelectItem key={opt.value} value={opt.value}>
                            <span className="flex items-center">
                              <span className={cn("w-3 h-3 rounded-full mr-2 border", opt.previewClass)}></span>
                              {opt.label}
                            </span>
                          </SelectItem>
                        ))}
                      </SelectGroup>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor={`fontSize-${columnDef.id}`} className="text-sm font-medium mb-2 block">
                  字号
                </Label>
                <Select
                  key={`font-size-${columnDef.id}-${currentFontSizeValue}`}
                  value={currentFontSizeValue}
                  onValueChange={(value) => onTextStyleChange('fontSize', value)}
                >
                  <SelectTrigger id={`fontSize-${columnDef.id}`}>
                    <SelectValue placeholder="选择字号" />
                  </SelectTrigger>
                  <SelectContent>
                    {fontSizes.map((opt) => (
                      <SelectItem key={opt.value} value={opt.value}>
                        {opt.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor={`fontWeight-${columnDef.id}`} className="text-sm font-medium mb-2 block">
                  字重
                </Label>
                <Select
                  key={`font-weight-${columnDef.id}-${currentFontWeightValue}`}
                  value={currentFontWeightValue}
                  onValueChange={(value) => onTextStyleChange('fontWeight', value)}
                >
                  <SelectTrigger id={`fontWeight-${columnDef.id}`}>
                    <SelectValue placeholder="选择字重" />
                  </SelectTrigger>
                  <SelectContent>
                    {fontWeights.map((opt) => (
                      <SelectItem key={opt.value} value={opt.value}>
                        {opt.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </>
          )}
        </div>
        <DialogFooter>
          <Button type="button" onClick={() => onOpenChange(false)}>
            保存
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
    

    

    

    



