'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTaskSelectionState, useTaskSelectionActions, TaskSelectionProvider } from '@/contexts/TaskSelectionContext';
import { cn } from '@/lib/utils';
import { CheckCircle2, Circle } from 'lucide-react';

const TaskCardClickTestContent: React.FC = () => {
  const { selectedTask, selectedTaskId, isTaskSelected } = useTaskSelectionState();
  const { setSelectedTask, clearSelection } = useTaskSelectionActions();

  // 模拟任务数据
  const testTasks = [
    {
      id: 'task-1',
      taskNumber: 'T001',
      projectName: '测试项目A',
      dispatchStatus: 'pending',
      isDueForDispatch: false,
    },
    {
      id: 'task-2', 
      taskNumber: 'T002',
      projectName: '测试项目B',
      dispatchStatus: 'in_progress',
      isDueForDispatch: true,
    },
    {
      id: 'task-3',
      taskNumber: 'T003', 
      projectName: '测试项目C',
      dispatchStatus: 'completed',
      isDueForDispatch: false,
    },
    {
      id: 'task-4',
      taskNumber: 'T004',
      projectName: '测试项目D',
      dispatchStatus: 'pending',
      isDueForDispatch: false,
    }
  ];

  const handleCardClick = (task: any, e: React.MouseEvent) => {
    console.log('=== CARD CLICK TEST ===');
    console.log('Task:', task.taskNumber);
    console.log('setSelectedTask function:', typeof setSelectedTask);

    // 避免在点击按钮时触发卡片选中
    const target = e.target as HTMLElement;
    if (target.closest('button, a, input, select, textarea, [role="button"]')) {
      console.log('Click ignored - interactive element');
      return;
    }

    // 切换选中状态
    const isCurrentlySelected = isTaskSelected(task.id);
    console.log('Currently selected:', isCurrentlySelected);

    if (isCurrentlySelected) {
      console.log('Deselecting...');
      setSelectedTask(null);
    } else {
      console.log('Selecting...');
      setSelectedTask(task);
    }
    console.log('=== END CARD CLICK TEST ===');
  };

  // 模拟操作栏按钮状态
  const hasSelectedTask = !!selectedTaskId;

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">任务卡片单击选中测试</h2>
        
        {/* 选中状态显示 */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="font-semibold mb-2">当前选中状态:</h3>
          <div className="space-y-2">
            <p>选中任务ID: <Badge variant="outline">{selectedTaskId || '无'}</Badge></p>
            <p>选中任务编号: <Badge variant="outline">{selectedTask?.taskNumber || '无'}</Badge></p>
            <p>选中任务项目: <Badge variant="outline">{selectedTask?.projectName || '无'}</Badge></p>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-2">
          <Button 
            onClick={clearSelection}
            variant="outline"
            disabled={!hasSelectedTask}
          >
            清除选择
          </Button>
          <Button 
            disabled={!hasSelectedTask}
            variant={hasSelectedTask ? "default" : "secondary"}
          >
            发车 {hasSelectedTask && `(${selectedTask?.taskNumber})`}
          </Button>
          <Button 
            disabled={!hasSelectedTask}
            variant={hasSelectedTask ? "destructive" : "secondary"}
          >
            撤销任务 {hasSelectedTask && `(${selectedTask?.taskNumber})`}
          </Button>
        </div>

        {/* 状态说明 */}
        <div className="p-3 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-700">
            {hasSelectedTask 
              ? `按钮已启用，将操作任务: ${selectedTask?.taskNumber}` 
              : '所有按钮已禁用，请先选择一个任务'
            }
          </p>
        </div>
      </div>

      {/* 任务卡片网格 */}
      <div className="space-y-3">
        <h4 className="font-medium">任务卡片 (点击选中/取消选中):</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {testTasks.map(task => {
            const isSelected = isTaskSelected(task.id);
            
            return (
              <Card
                key={task.id}
                data-task-id={task.id}
                onClick={(e) => handleCardClick(task, e)}
                className={cn(
                  "p-4 cursor-pointer transition-all duration-200 ease-in-out task-card task-card-clickable",
                  "hover:shadow-md",
                  isSelected && "task-row-selected",
                  task.isDueForDispatch && "ring-2 ring-orange-200"
                )}
              >
                <div className="space-y-3">
                  {/* 卡片头部 */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {isSelected ? (
                        <CheckCircle2 className="w-5 h-5 text-blue-600" />
                      ) : (
                        <Circle className="w-5 h-5 text-gray-400" />
                      )}
                      <span className="font-semibold">{task.taskNumber}</span>
                    </div>
                    <Badge 
                      variant={task.dispatchStatus === 'completed' ? 'default' : 
                               task.dispatchStatus === 'in_progress' ? 'secondary' : 'outline'}
                    >
                      {task.dispatchStatus === 'completed' ? '已完成' :
                       task.dispatchStatus === 'in_progress' ? '进行中' : '待发车'}
                    </Badge>
                  </div>

                  {/* 项目信息 */}
                  <div>
                    <p className="text-sm text-gray-600">{task.projectName}</p>
                  </div>

                  {/* 选中状态指示 */}
                  <div className="text-xs text-gray-500">
                    状态: {isSelected ? '已选中' : '未选中'}
                  </div>

                  {/* 测试按钮 - 应该不触发卡片选中 */}
                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        console.log('Button clicked, should not select card');
                      }}
                    >
                      测试按钮
                    </Button>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      {/* 使用说明 */}
      <div className="p-4 bg-yellow-50 rounded-lg">
        <h4 className="font-medium mb-2">测试说明:</h4>
        <ul className="text-sm text-gray-700 space-y-1">
          <li>• 点击任务卡片可以选中/取消选中</li>
          <li>• 选中状态会显示在卡片上和顶部状态区域</li>
          <li>• 操作按钮会根据选中状态启用/禁用</li>
          <li>• 点击卡片内的按钮不会触发选中</li>
          <li>• 选中的卡片会有特殊的背景色和边框样式</li>
        </ul>
      </div>
    </div>
  );
};

export const TaskCardClickTest: React.FC = () => {
  return (
    <TaskSelectionProvider>
      <TaskCardClickTestContent />
    </TaskSelectionProvider>
  );
};

export default TaskCardClickTest;
