
// src/services/deliveryOrderService.ts
import type { DeliveryOrder } from '@/types';

/**
 * Filters delivery orders for a specific production line within a selected plant.
 * @param allDeliveryOrders - All available delivery orders.
 * @param selectedPlantId - The ID of the currently selected plant. Can be null.
 * @param productionLineId - The ID of the production line to filter for.
 * @returns An array of delivery orders for the specified line and plant.
 */
export function filterDeliveryOrdersForProductionLine(
  allDeliveryOrders: DeliveryOrder[],
  selectedPlantId: string | null,
  productionLineId: string
): DeliveryOrder[] {
  if (!selectedPlantId || !Array.isArray(allDeliveryOrders)) {
    return [];
  }
  return allDeliveryOrders.filter(
    (order) =>
      order.plantId === selectedPlantId &&
      order.productionLineId === productionLineId
  );
}
