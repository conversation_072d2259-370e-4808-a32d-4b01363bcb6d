{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 9006", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@genkit-ai/googleai": "^1.8.0", "@genkit-ai/next": "^1.8.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.66.0", "@tanstack/react-table": "^8.19.3", "@tanstack/react-virtual": "^3.8.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "firebase": "^11.8.1", "genkit": "^1.8.0", "idb": "^8.0.3", "immer": "^10.1.1", "lodash-es": "^4.17.21", "lucide-react": "^0.475.0", "next": "15.2.4", "patch-package": "^8.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-hooks-worker": "^1.0.3", "react-virtuoso": "^4.12.8", "react-window": "^1.8.11", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2", "zustand": "^4.5.4"}, "devDependencies": {"@opentelemetry/exporter-jaeger": "^2.0.1", "@types/lodash-es": "^4.17.12", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-window": "^1.8.8", "genkit-cli": "^1.8.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}