import React from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { TaskGroupConfig } from '@/types';
import { getGroupingOptions } from '@/utils/task-grouping';
import {
  Settings,
  ChevronDown,
  BarChart3,
  Eye,
  EyeOff,
  FoldVertical,
  UnfoldVertical,
  SortAsc,
  SortDesc,
} from 'lucide-react';

interface TaskGroupToolbarProps {
  groupConfig: TaskGroupConfig;
  totalGroups: number;
  collapsedGroups: number;
  onToggleGrouping: () => void;
  onSetGroupBy: (groupBy: TaskGroupConfig['groupBy']) => void;
  onOpenConfig: () => void;
  onExpandAll?: () => void;
  onCollapseAll?: () => void;
  onToggleSortOrder?: () => void;
}

export function TaskGroupToolbar({
  groupConfig,
  totalGroups,
  collapsedGroups,
  onToggleGrouping,
  onSetGroupBy,
  onOpenConfig,
  onExpandAll,
  onCollapseAll,
  onToggleSortOrder,
}: TaskGroupToolbarProps) {
  const groupingOptions = getGroupingOptions();
  const currentOption = groupingOptions.find(opt => opt.value === groupConfig.groupBy);
  const hasCollapsedGroups = collapsedGroups > 0;
  const hasExpandedGroups = totalGroups > collapsedGroups;

  return (
    <div className="flex items-center gap-2 p-2 border-b bg-muted/30">
      {/* 分组开关 */}
      <Button
        variant={groupConfig.enabled ? 'default' : 'outline'}
        size="sm"
        onClick={onToggleGrouping}
        className="gap-2"
      >
        {groupConfig.enabled ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
        {groupConfig.enabled ? '已分组' : '未分组'}
      </Button>

      {/* 分组字段选择 */}
      {groupConfig.enabled && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="gap-2">
              <span>{currentOption?.icon}</span>
              <span>{currentOption?.label}</span>
              <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            <DropdownMenuLabel>选择分组字段</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {groupingOptions.map((option) => (
              <DropdownMenuCheckboxItem
                key={option.value}
                checked={groupConfig.groupBy === option.value}
                onCheckedChange={() => onSetGroupBy(option.value)}
              >
                <div className="flex items-center gap-2">
                  <span>{option.icon}</span>
                  <span>{option.label}</span>
                </div>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {/* 分组统计 */}
      {groupConfig.enabled && totalGroups > 0 && (
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="gap-1">
            <BarChart3 className="h-3 w-3" />
            {totalGroups} 组
          </Badge>
          {collapsedGroups > 0 && (
            <Badge variant="outline" className="gap-1">
              <FoldVertical className="h-3 w-3" />
              {collapsedGroups} 已折叠
            </Badge>
          )}
        </div>
      )}

      <div className="flex-1" />

      {/* 右侧操作按钮 */}
      {groupConfig.enabled && groupConfig.collapsible && (
        <div className="flex items-center gap-1">
          {hasCollapsedGroups && onExpandAll && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onExpandAll}
              className="gap-1 text-xs"
              title="展开所有分组"
            >
              <UnfoldVertical className="h-3 w-3" />
              展开全部
            </Button>
          )}
          {hasExpandedGroups && onCollapseAll && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onCollapseAll}
              className="gap-1 text-xs"
              title="折叠所有分组"
            >
              <FoldVertical className="h-3 w-3" />
              折叠全部
            </Button>
          )}
        </div>
      )}

      {/* 排序切换 */}
      {groupConfig.enabled && onToggleSortOrder && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleSortOrder}
          className="gap-1"
          title={`当前: ${groupConfig.sortOrder === 'asc' ? '升序' : '降序'}，点击切换`}
        >
          {groupConfig.sortOrder === 'asc' ? (
            <SortAsc className="h-4 w-4" />
          ) : (
            <SortDesc className="h-4 w-4" />
          )}
        </Button>
      )}

      {/* 分组配置 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={onOpenConfig}
        className="gap-1"
        title="分组配置"
      >
        <Settings className="h-4 w-4" />
      </Button>
    </div>
  );
}

/**
 * 紧凑版分组工具栏
 */
export function CompactTaskGroupToolbar({
  groupConfig,
  totalGroups,
  onToggleGrouping,
  onSetGroupBy,
  onOpenConfig,
}: Pick<TaskGroupToolbarProps, 'groupConfig' | 'totalGroups' | 'onToggleGrouping' | 'onSetGroupBy' | 'onOpenConfig'>) {
  const groupingOptions = getGroupingOptions();
  const currentOption = groupingOptions.find(opt => opt.value === groupConfig.groupBy);

  return (
    <div className="flex items-center gap-2 p-1 border-b bg-muted/20">
      <Button
        variant={groupConfig.enabled ? 'default' : 'ghost'}
        size="sm"
        onClick={onToggleGrouping}
        className="gap-1 text-xs"
      >
        {groupConfig.enabled ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
        分组
      </Button>

      {groupConfig.enabled && (
        <>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="gap-1 text-xs">
                <span>{currentOption?.icon}</span>
                <ChevronDown className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              {groupingOptions.map((option) => (
                <DropdownMenuItem
                  key={option.value}
                  onClick={() => onSetGroupBy(option.value)}
                  className={groupConfig.groupBy === option.value ? 'bg-accent' : ''}
                >
                  <div className="flex items-center gap-2">
                    <span>{option.icon}</span>
                    <span>{option.label}</span>
                  </div>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <Badge variant="outline" className="text-xs">
            {totalGroups} 组
          </Badge>
        </>
      )}

      <div className="flex-1" />

      <Button
        variant="ghost"
        size="sm"
        onClick={onOpenConfig}
        className="gap-1 text-xs"
      >
        <Settings className="h-3 w-3" />
      </Button>
    </div>
  );
}