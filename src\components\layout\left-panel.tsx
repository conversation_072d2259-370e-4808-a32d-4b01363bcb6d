
'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { TaskList } from '@/components/sections/task-list/task-list';
import { DeliveryOrderList } from '@/components/sections/delivery-order-list';
import { cn } from '@/lib/utils';
import { useUiStore } from '@/store/uiStore';
import { Button } from '@/components/ui/button';
import { ListChecks } from 'lucide-react';
import { useCurrentPlantInfo } from '@/hooks/useCurrentPlantInfo'; // Import the new hook

const MemoizedTaskList = React.memo(TaskList);
const MemoizedDeliveryOrderList = React.memo(DeliveryOrderList);

// Removed productionLineCount from props
export function LeftPanel() {
  const taskStatusFilter = useUiStore(state => state.taskStatusFilter);
  // Use the hook to get productionLineCount
  const { productionLineCount, isLoadingPlants } = useCurrentPlantInfo();
  const [deliveryListExpanded, setDeliveryListExpanded] = useState(true);

  const leftPanelRef = useRef<HTMLDivElement>(null);
  const [deliveryListHeight, setDeliveryListHeight] = useState(150);
  const [isResizing, setIsResizing] = useState(false);
  const dragHandleRef = useRef<{ startY: number; initialHeight: number } | null>(null);

  const showDeliveryOrders = taskStatusFilter === 'InProgress';

  const handleCollapseDeliveryList = useCallback(() => {
    setDeliveryListExpanded(false);
  }, []);

  const handleMouseDownOnDivider = useCallback((e: React.MouseEvent) => {
    if (!showDeliveryOrders || !deliveryListExpanded) return;
    e.preventDefault();
    setIsResizing(true);
    dragHandleRef.current = {
      startY: e.clientY,
      initialHeight: deliveryListHeight,
    };
    if (typeof document !== 'undefined' && document.body) {
      document.body.style.cursor = 'row-resize';
      document.body.style.userSelect = 'none';
    }
  }, [showDeliveryOrders, deliveryListExpanded, deliveryListHeight]);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing || !dragHandleRef.current || !leftPanelRef.current) return;
      const deltaY = e.clientY - dragHandleRef.current.startY;
      let newHeight = dragHandleRef.current.initialHeight - deltaY; // Dragging down decreases height of bottom panel
      
      const minHeight = 50; // Min height for delivery list
      const taskListMinHeight = 100; // Min height for task list
      const resizerHeight = 6; // Height of the resizer div (h-1.5 = 6px)
      const panelHeight = leftPanelRef.current.offsetHeight;
      const maxHeight = panelHeight - taskListMinHeight - resizerHeight;

      newHeight = Math.max(minHeight, Math.min(newHeight, maxHeight));
      setDeliveryListHeight(newHeight);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      if (typeof document !== 'undefined' && document.body) {
        document.body.style.cursor = 'default';
        document.body.style.userSelect = '';
      }
      dragHandleRef.current = null;
    };

    if (isResizing) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
      if (typeof document !== 'undefined' && document.body) {
        if (document.body.style.cursor === 'row-resize') {
          document.body.style.cursor = 'default';
        }
        if (document.body.style.userSelect === 'none') {
          document.body.style.userSelect = '';
        }
      }
    };
  }, [isResizing]);

  const taskListContainerStyle = (!showDeliveryOrders || !deliveryListExpanded)
    ? { height: '100%', minHeight: 0 }
    : { flex: '1 1 0%', minHeight: '100px', height: 'auto' };

  if (isLoadingPlants) {
    // Optionally, show a loading state if productionLineCount depends on plant data loading
    return <div className="flex items-center justify-center h-full">Loading plant info...</div>;
  }

  return (
    <div ref={leftPanelRef} className="flex flex-col h-full space-y-0.5 p-0.5 pr-0 relative">
      <div
        className={cn(
          "min-h-0 transition-all duration-300 ease-in-out", // Keep transition for expand/collapse
          "flex-1"
        )}
        style={taskListContainerStyle}
      >
        <MemoizedTaskList
          productionLineCount={productionLineCount} // Pass the count from the hook
        />
      </div>

      {showDeliveryOrders && deliveryListExpanded && (
        <div
          onMouseDown={handleMouseDownOnDivider}
          className={cn(
            "h-1.5 bg-border hover:bg-primary/30 cursor-row-resize transition-colors flex-shrink-0 z-30",
            isResizing && "bg-primary"
          )}
          title="拖动调整高度"
        />
      )}

      {showDeliveryOrders && (
        <div
          className={cn(
            "overflow-hidden flex-shrink-0 bg-card relative z-30 transition-[height] duration-300 ease-in-out", 
            // Removed transition-[height] to allow smooth drag resizing
            // "transition-[height] duration-300 ease-in-out"
          )}
          style={{ height: deliveryListExpanded ? `${deliveryListHeight}px` : '0px' }}
        >
          {deliveryListExpanded && deliveryListHeight > 20 && ( // Render content only if height is sufficient
            <MemoizedDeliveryOrderList
              onCollapse={handleCollapseDeliveryList}
              isExpanded={deliveryListExpanded}
            />
          )}
        </div>
      )}

    </div>
  );
}
