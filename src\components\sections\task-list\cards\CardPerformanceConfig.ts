/**
 * 卡片性能优化配置
 * 用于控制动画、渲染和滚动性能
 */

export interface PerformanceConfig {
  // 虚拟滚动配置
  virtualScroll: {
    enabled: boolean;
    overscan: number;
    increaseViewportBy: number;
    itemHeight: number;
  };
  
  // 动画配置
  animations: {
    enabled: boolean;
    duration: number;
    easing: string;
    reduceMotion: boolean;
  };
  
  // 渲染优化
  rendering: {
    useMemo: boolean;
    useCallback: boolean;
    reactMemo: boolean;
    debounceMs: number;
  };
  
  // 滚动优化
  scrolling: {
    smoothScrolling: boolean;
    scrollBehavior: 'auto' | 'smooth';
    throttleMs: number;
  };
}

// 默认性能配置
export const defaultPerformanceConfig: PerformanceConfig = {
  virtualScroll: {
    enabled: true,
    overscan: 1,
    increaseViewportBy: 100,
    itemHeight: 300,
  },
  animations: {
    enabled: true,
    duration: 250,
    easing: 'ease-out',
    reduceMotion: false,
  },
  rendering: {
    useMemo: true,
    useCallback: true,
    reactMemo: true,
    debounceMs: 100,
  },
  scrolling: {
    smoothScrolling: true,
    scrollBehavior: 'smooth',
    throttleMs: 16,
  },
};

// 高性能配置（适合大量数据）
export const highPerformanceConfig: PerformanceConfig = {
  virtualScroll: {
    enabled: true,
    overscan: 0,
    increaseViewportBy: 50,
    itemHeight: 280,
  },
  animations: {
    enabled: false,
    duration: 150,
    easing: 'ease-out',
    reduceMotion: true,
  },
  rendering: {
    useMemo: true,
    useCallback: true,
    reactMemo: true,
    debounceMs: 50,
  },
  scrolling: {
    smoothScrolling: false,
    scrollBehavior: 'auto',
    throttleMs: 8,
  },
};

// 流畅体验配置（适合演示）
export const smoothExperienceConfig: PerformanceConfig = {
  virtualScroll: {
    enabled: true,
    overscan: 3,
    increaseViewportBy: 300,
    itemHeight: 320,
  },
  animations: {
    enabled: true,
    duration: 300,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    reduceMotion: false,
  },
  rendering: {
    useMemo: true,
    useCallback: true,
    reactMemo: true,
    debounceMs: 150,
  },
  scrolling: {
    smoothScrolling: true,
    scrollBehavior: 'smooth',
    throttleMs: 16,
  },
};

// 根据设备性能自动选择配置
export const getOptimalPerformanceConfig = (): PerformanceConfig => {
  // 检测设备性能
  const isLowEndDevice = () => {
    // 检查内存
    const memory = (navigator as any).deviceMemory;
    if (memory && memory < 4) return true;
    
    // 检查CPU核心数
    const cores = navigator.hardwareConcurrency;
    if (cores && cores < 4) return true;
    
    // 检查用户代理（移动设备）
    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    if (isMobile) return true;
    
    return false;
  };
  
  // 检测用户偏好
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  
  if (isLowEndDevice() || prefersReducedMotion) {
    return {
      ...highPerformanceConfig,
      animations: {
        ...highPerformanceConfig.animations,
        reduceMotion: prefersReducedMotion,
      },
    };
  }
  
  return defaultPerformanceConfig;
};

// CSS 变量生成器
export const generatePerformanceCSSVars = (config: PerformanceConfig) => {
  return {
    '--animation-duration': `${config.animations.duration}ms`,
    '--animation-easing': config.animations.easing,
    '--scroll-behavior': config.scrolling.scrollBehavior,
  };
};

// 性能监控工具
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();
  
  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }
  
  // 记录渲染时间
  recordRenderTime(componentName: string, duration: number) {
    if (!this.metrics.has(componentName)) {
      this.metrics.set(componentName, []);
    }
    
    const times = this.metrics.get(componentName)!;
    times.push(duration);
    
    // 只保留最近100次记录
    if (times.length > 100) {
      times.shift();
    }
  }
  
  // 获取平均渲染时间
  getAverageRenderTime(componentName: string): number {
    const times = this.metrics.get(componentName);
    if (!times || times.length === 0) return 0;
    
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }
  
  // 获取性能报告
  getPerformanceReport(): Record<string, { avg: number; count: number; max: number }> {
    const report: Record<string, { avg: number; count: number; max: number }> = {};
    
    this.metrics.forEach((times, componentName) => {
      if (times.length > 0) {
        report[componentName] = {
          avg: this.getAverageRenderTime(componentName),
          count: times.length,
          max: Math.max(...times),
        };
      }
    });
    
    return report;
  }
  
  // 清除指标
  clearMetrics() {
    this.metrics.clear();
  }
}

// 性能优化 Hook
export const usePerformanceOptimization = (componentName: string) => {
  const monitor = PerformanceMonitor.getInstance();
  
  const measureRender = (fn: () => void) => {
    const start = performance.now();
    fn();
    const end = performance.now();
    monitor.recordRenderTime(componentName, end - start);
  };
  
  return { measureRender, monitor };
};

// 滚动性能优化工具
export const optimizeScrollPerformance = (element: HTMLElement, config: PerformanceConfig) => {
  // 启用 CSS 包含
  element.style.contain = 'layout style paint';
  
  // 启用硬件加速
  element.style.transform = 'translateZ(0)';
  element.style.willChange = 'transform';
  
  // 设置滚动行为
  element.style.scrollBehavior = config.scrolling.scrollBehavior;
  
  // 添加滚动优化类
  element.classList.add('scroll-optimized');
};

// 动画性能优化
export const optimizeAnimationPerformance = (element: HTMLElement, config: PerformanceConfig) => {
  if (!config.animations.enabled) {
    element.style.transition = 'none';
    element.style.animation = 'none';
    return;
  }
  
  // 使用 transform 而不是改变 layout 属性
  element.style.transition = `transform ${config.animations.duration}ms ${config.animations.easing}`;
  
  // 启用硬件加速
  element.style.transform = 'translateZ(0)';
  element.style.willChange = 'transform';
  
  // 减少动画的复杂度
  if (config.animations.reduceMotion) {
    element.style.transition = `opacity ${config.animations.duration / 2}ms ease-out`;
  }
};
