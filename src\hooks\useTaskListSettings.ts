// src/hooks/useTaskListSettings.ts
'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { flushSync } from 'react-dom';
import type {
  TaskListDisplayMode,
  TaskListDensityMode,
  CustomColumnDefinition,
  ColumnTextStyles,
  InTaskVehicleCardStyle,
  TaskListStoredSettings,
  TaskColumnId,
  StyleableColumnId,
  ColumnTextStyle,
  DensityStyleValues,
  TaskGroupConfig,
  TaskGroup,
} from '@/types';
import { useToast } from '@/hooks/use-toast';
import { ALL_TASK_COLUMNS_CONFIG } from '@/components/sections/task-list/task-list.config';
import { getDensityNumericConfig } from '@/components/sections/task-list/task-list-density-config';
import { produce } from 'immer';
import { immerSetState } from '@/utils/immer-helpers';

const TASK_LIST_SETTINGS_KEY = 'taskListSettings_v3.3'; // Incremented version

// Initial column order derived from ALL_TASK_COLUMNS_CONFIG sorted by 'order'
const initialColumnOrderForInit = ALL_TASK_COLUMNS_CONFIG
  .slice() // Create a copy before sorting
  .sort((a, b) => (a.order || 999) - (b.order || 999))
  .map(c => c.id as string);

const initialColumnVisibilityForInit = ALL_TASK_COLUMNS_CONFIG.reduce((acc, colDef) => {
  acc[colDef.id as string] = colDef.isMandatory || colDef.defaultVisible || false;
  if (colDef.id === 'completedProgress' && (colDef.defaultVisible || colDef.isMandatory)) {
    acc.requiredVolume = false;
    acc.completedVolume = false;
  }
  return acc;
}, {} as Record<string, boolean>);

const initialColumnWidthsForInit = ALL_TASK_COLUMNS_CONFIG.reduce((acc, colDef) => {
  if (colDef.defaultWidth) {
    acc[colDef.id as string] = colDef.defaultWidth;
  }
  return acc;
}, {} as Record<string, number>);

const initialInTaskVehicleCardStyles: InTaskVehicleCardStyle = {
  cardWidth: 'w-14',
  cardHeight: 'h-8',
  fontSize: 'text-[12px]',
  fontColor: 'text-foreground',
  vehicleNumberFontWeight: 'font-medium',
  cardBgColor: 'bg-card/80', // Default card background
  cardGradient: undefined,
  gradientEnabled: false,
  gradientDirection: 'to-r',
  gradientStartColor: '#3b82f6',
  gradientEndColor: '#8b5cf6',
  statusDotSize: 'w-1 h-1',
  borderRadius: 'rounded-md',
  boxShadow: 'shadow-sm',
  vehiclesPerRow: 4,
  gap: 0,
  cardSize: 'small'
};

/**
 * 默认任务分组配置
 * 提供美观且实用的分组功能默认设置
 */
const initialTaskGroupConfig: TaskGroupConfig = {
  groupBy: 'none',
  enabled: false,
  collapsible: true,
  defaultCollapsed: [],
  sortOrder: 'asc',
  showGroupStats: true,
  allowedGroupColumns: [
    'projectName', 
    'strength',
    'pouringMethod','supplyDate','pumpTruck','constructionUnit','constructionSite'
  ],
  disallowedGroupColumns: [
    'taskNumber',
    'vehicleCount',
    'completedVolume',
    'requiredVolume',
    'contactPhone',
    'supplyTime',
    'publishDate',
    'dispatchedVehicles'
  ],
  groupHeaderStyle: {
    backgroundColor: 'bg-muted/50',
    textColor: 'text-foreground',
    fontSize: 'text-sm',
    fontWeight: 'font-medium',
    padding: 'py-2',
  },
};

const initialTaskListSettings: TaskListStoredSettings = {
  displayMode: 'table',
  density: 'compact', // Changed to 'compact' as requested
  enableZebraStriping: false,
  columnOrder: initialColumnOrderForInit,
  columnVisibility: initialColumnVisibilityForInit,
  columnWidths: initialColumnWidthsForInit,
  columnTextStyles: {}, 
  columnBackgrounds: {}, 
  inTaskVehicleCardStyles: initialInTaskVehicleCardStyles,
  selectedPlantId: null,
  groupConfig: initialTaskGroupConfig,
};




export function useTaskListSettings() {
  const [settings, setSettingsState] = useState<TaskListStoredSettings>(initialTaskListSettings);
  const [isSettingsLoaded, setIsSettingsLoaded] = useState(false);
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [isColumnVisibilityModalOpen, setIsColumnVisibilityModalOpen] = useState(false);
  const [isColumnSpecificStyleModalOpen, setIsColumnSpecificStyleModalOpen] = useState(false);
  const [editingColumnDef, setEditingColumnDef] = useState<CustomColumnDefinition | null>(null);
  const [isStyleEditorModalOpen, setIsStyleEditorModalOpen] = useState(false);
  const [isGroupConfigModalOpen, setIsGroupConfigModalOpen] = useState(false);


  useEffect(() => {
    const loadSettings = () => {
      try {
        // 从localStorage加载设置
        const stored = localStorage.getItem(TASK_LIST_SETTINGS_KEY);
        const loadedSettings = stored ? JSON.parse(stored) : initialTaskListSettings;

        // 与当前模式进行协调
        const reconciledSettings: TaskListStoredSettings = {
          ...loadedSettings,
          // 保留加载的样式配置，不要被初始值覆盖
          columnTextStyles: loadedSettings.columnTextStyles || {},
          columnBackgrounds: loadedSettings.columnBackgrounds || {},
          inTaskVehicleCardStyles: {
            ...initialInTaskVehicleCardStyles,
            ...(loadedSettings.inTaskVehicleCardStyles || {}),
          },
          groupConfig: {
            ...initialTaskGroupConfig,
            ...(loadedSettings.groupConfig || {}),
            groupHeaderStyle: {
              ...initialTaskGroupConfig.groupHeaderStyle,
              ...(loadedSettings.groupConfig?.groupHeaderStyle || {}),
            },
          },
        };

        // Column Order Reconciliation
        const allColumnIdsFromConfigSorted = ALL_TASK_COLUMNS_CONFIG
            .slice()
            .sort((a, b) => (a.order || 999) - (b.order || 999))
            .map(c => c.id as string);

        if (loadedSettings.columnOrder && Array.isArray(loadedSettings.columnOrder)) {
            const currentConfigIdsSet = new Set(allColumnIdsFromConfigSorted);
            const validStoredOrder = loadedSettings.columnOrder.filter((id: string) => currentConfigIdsSet.has(id));
            const allCurrentColsInStoredOrder = validStoredOrder.length === allColumnIdsFromConfigSorted.length &&
                                                allColumnIdsFromConfigSorted.every(id => validStoredOrder.includes(id));

            if (allCurrentColsInStoredOrder) {
                reconciledSettings.columnOrder = validStoredOrder;
            } else {
                // Stored order is incomplete or contains obsolete columns. Reset to default.
                reconciledSettings.columnOrder = allColumnIdsFromConfigSorted;
                console.warn("TaskListSettings: Stored columnOrder was incompatible or incomplete. Reverted to default order.");
                if (isSettingsLoaded) { // Avoid toast on initial load if settings were just bad
                    toast({ title: "列顺序已重置", description: "部分列配置已更新，列的显示顺序已恢复为默认。您可以重新排序。", variant: "default", duration: 7000});
                }
            }
        } else {
            reconciledSettings.columnOrder = allColumnIdsFromConfigSorted;
        }

        // Column Visibility Reconciliation
        const newVisibility: Record<string, boolean> = {};
        ALL_TASK_COLUMNS_CONFIG.forEach(colDef => {
          if (colDef.isMandatory) {
            newVisibility[colDef.id as string] = true;
          } else if (loadedSettings.columnVisibility && loadedSettings.columnVisibility.hasOwnProperty(colDef.id as string)) {
            newVisibility[colDef.id as string] = loadedSettings.columnVisibility[colDef.id as string];
          } else {
            newVisibility[colDef.id as string] = colDef.defaultVisible || false;
          }
        });
        if (newVisibility.completedProgress) {
          newVisibility.requiredVolume = false;
          newVisibility.completedVolume = false;
        } else if (newVisibility.requiredVolume !== false || newVisibility.completedVolume !== false) {
          newVisibility.completedProgress = false;
        }
        reconciledSettings.columnVisibility = newVisibility;

        // Column Widths Reconciliation
        const newWidths: Record<string, number> = {};
        ALL_TASK_COLUMNS_CONFIG.forEach(colDef => {
          newWidths[colDef.id as string] = (loadedSettings.columnWidths && loadedSettings.columnWidths[colDef.id as string]) || colDef.defaultWidth || 100;
        });
        reconciledSettings.columnWidths = newWidths;

        setSettingsState(reconciledSettings);
      } catch (error) {
        console.error("Failed to load task list settings:", error);
        setSettingsState(initialTaskListSettings);
      }
      setIsSettingsLoaded(true);
    };

    loadSettings();
  }, [toast, isSettingsLoaded]); // Added isSettingsLoaded to dependency array to avoid toast on first load if settings were bad

  useEffect(() => {
    if (!isSettingsLoaded) return;
    try {
      localStorage.setItem(TASK_LIST_SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }, [settings, isSettingsLoaded]);

  const updateSetting = useCallback(<K extends keyof TaskListStoredSettings>(
    key: K,
    value: TaskListStoredSettings[K]
  ) => {
    setSettingsState(prev => ({ ...prev, [key]: value }));
  }, []);
  
 // 优化列可见性更新
const handleColumnVisibilityChange = useCallback((columnId: string, checked: boolean) => {
  setSettingsState(produce((draft) => {
    draft.columnVisibility[columnId] = checked;
    
    if (columnId === 'completedProgress' && checked) {
      draft.columnVisibility.requiredVolume = false;
      draft.columnVisibility.completedVolume = false;
    } else if ((columnId === 'requiredVolume' || columnId === 'completedVolume') && checked) {
      if (columnId === 'requiredVolume' ? draft.columnVisibility.completedVolume !== false : draft.columnVisibility.requiredVolume !== false) {
        draft.columnVisibility.completedProgress = false;
      }
    }
  }));
}, []);

  const handleColumnOrderChange = useCallback((newOrder: string[]) => {
    updateSetting('columnOrder', newOrder);
  }, [updateSetting]);

  const handleSingleColumnWidthChange = useCallback((columnId: string, width: number) => {
    setSettingsState(prev => {
      const newWidths = { ...prev.columnWidths, [columnId]: width };
      return { ...prev, columnWidths: newWidths };
    });
  }, []);

 // 字段样式更新
const handleColumnTextStyleChange = useCallback(
  (columnId: StyleableColumnId, styleProperty: keyof ColumnTextStyle, valueKey: string) => {
    flushSync(() => {
      setSettingsState(produce((draft) => {
        if (!draft.columnTextStyles[columnId]) {
          draft.columnTextStyles[columnId] = {};
        }

        if (valueKey === 'default') {
          delete draft.columnTextStyles[columnId][styleProperty];
          // 如果对象为空，删除整个键
          if (Object.keys(draft.columnTextStyles[columnId]).length === 0) {
            delete draft.columnTextStyles[columnId];
          }
        } else {
          draft.columnTextStyles[columnId][styleProperty] = valueKey;
        }
      }));
    });
  },
  []
);

 const handleColumnBackgroundChange = useCallback((columnId: string, valueKeyToStore: string) => {
    flushSync(() => {
      setSettingsState(prev => {
        const newBackgrounds = { ...prev.columnBackgrounds };
        if (valueKeyToStore === 'default-solid' || valueKeyToStore === 'default-semi' || valueKeyToStore === '') {
          delete newBackgrounds[columnId];
        } else {
          newBackgrounds[columnId] = valueKeyToStore;
        }
        return { ...prev, columnBackgrounds: newBackgrounds };
      });
    });
  }, []);

  const handleVehiclesPerRowChange = useCallback((vehiclesPerRow: 2 | 3 | 4 | 5 | 6 | 7 | 8) => {
    setSettingsState(prev => {
      const cardWidthString = prev.inTaskVehicleCardStyles.cardWidth || 'w-14'; 
      const cardWidthPx = (() => {
        switch (cardWidthString) {
          case 'w-8': return 32;
          case 'w-9': return 36;
          case 'w-10': return 40;
          case 'w-12': return 48;
          case 'w-14': return 56;
          case 'w-16': return 64;
          case 'w-20': return 80;
          case 'w-24': return 96;
          default: {
            const match = (cardWidthString as string).match(/w-(\d+)/);
            return match ? parseInt(match[1], 10) * 4 : 56; 
          }
        }
      })();
      
      const currentDensity = prev.density || 'normal';
      const densityConfig = getDensityNumericConfig(currentDensity);
      const gapPx = densityConfig.gap;
      const cellPaddingPx = densityConfig.cellHorizontalPadding;
      
      const extraWidthForScrollbarEtc = 11; 
      
      const newColumnWidth = 
          (vehiclesPerRow * cardWidthPx) + 
          ((vehiclesPerRow - 1) * gapPx) + 
          cellPaddingPx + 
          extraWidthForScrollbarEtc;
      
      return {
        ...prev,
        inTaskVehicleCardStyles: {
          ...prev.inTaskVehicleCardStyles,
          vehiclesPerRow
        },
        columnWidths: {
          ...prev.columnWidths,
          dispatchedVehicles: Math.max(150, Math.round(newColumnWidth)) 
        }
      };
    });
  }, []);

  const handleCustomDispatchVehiclesColumnWidth = useCallback((width: number) => {
    setSettingsState(prev => {
      return {
        ...prev,
        columnWidths: {
          ...prev.columnWidths,
          dispatchedVehicles: width
        }
      };
    });
  }, []);

  const resetAllSettings = useCallback(() => {
    try {
      localStorage.removeItem(TASK_LIST_SETTINGS_KEY);
      setSettingsState(initialTaskListSettings);
      toast({ title: "样式已重置", description: "任务列表样式已恢复为默认设置。" });
    } catch (error) {
      console.error("Failed to reset settings:", error);
      setSettingsState(initialTaskListSettings);
      toast({ title: "重置失败", description: "无法重置配置，已恢复为初始设置。", variant: "destructive" });
    }
  }, [toast]);

  /**
   * 更新分组配置
   */
  const updateGroupConfig = useCallback((newGroupConfig: Partial<TaskGroupConfig>) => {
  setSettingsState(produce((draft) => {
    Object.assign(draft.groupConfig, newGroupConfig);
    if (newGroupConfig.groupHeaderStyle) {
      Object.assign(draft.groupConfig.groupHeaderStyle, newGroupConfig.groupHeaderStyle);
    }
  }));
}, []);

  /**
   * 切换分组功能开关
   */
  const handleToggleGrouping = useCallback(() => {
    setSettingsState(prev => ({
      ...prev,
      groupConfig: {
        ...prev.groupConfig,
        enabled: !prev.groupConfig.enabled,
      },
    }));
  }, []);

  /**
   * 设置分组字段
   */
  const handleSetGroupBy = useCallback((groupBy: TaskGroupConfig['groupBy']) => {
    setSettingsState(prev => ({
      ...prev,
      groupConfig: {
        ...prev.groupConfig,
        groupBy,
        enabled: groupBy !== 'none',
      },
    }));
  }, []);

  /**
   * 切换分组折叠状态 - 优化版本，减少状态更新频率
   */
  const handleToggleGroupCollapse = useCallback((groupKey: string) => {
    // 使用 requestAnimationFrame 来优化性能
    requestAnimationFrame(() => {
      setSettingsState(prev => {
        const currentCollapsed = prev.groupConfig.defaultCollapsed || [];
        const isCollapsed = currentCollapsed.includes(groupKey);
        
        return {
          ...prev,
          groupConfig: {
            ...prev.groupConfig,
            defaultCollapsed: isCollapsed
              ? currentCollapsed.filter(key => key !== groupKey)
              : [...currentCollapsed, groupKey],
          },
        };
      });
    });
  }, []);

  const exportSettings = useCallback(() => {
    if (!isSettingsLoaded) {
      toast({ title: "设置尚未加载", description: "请稍后再试。", variant: "destructive" });
      return;
    }
    const jsonString = JSON.stringify(settings, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'task-list-settings.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast({ title: "配置已导出", description: "任务列表样式配置已导出。" });
  }, [settings, toast, isSettingsLoaded]);

  const handleImportFile = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = e.target?.result;
        if (typeof text !== 'string') throw new Error("File content is not a string.");
        
        const imported = JSON.parse(text) as Partial<TaskListStoredSettings>;
        const reconciled: TaskListStoredSettings = {
          ...initialTaskListSettings, 
          ...imported, 
          inTaskVehicleCardStyles: {
            ...initialTaskListSettings.inTaskVehicleCardStyles,
            ...(imported.inTaskVehicleCardStyles || {}),
             cardGradient: imported.inTaskVehicleCardStyles?.cardGradient, // Ensure gradient is loaded
          },

          columnBackgrounds: imported.columnBackgrounds || {},
          columnTextStyles: imported.columnTextStyles || {},
          groupConfig: {
            ...initialTaskGroupConfig,
            ...(imported.groupConfig || {}),
            groupHeaderStyle: {
              ...initialTaskGroupConfig.groupHeaderStyle,
              ...(imported.groupConfig?.groupHeaderStyle || {}),
            },
          },
        };

        // Column Order Reconciliation on import
        const allColumnIdsFromConfigSortedOnImport = ALL_TASK_COLUMNS_CONFIG
            .slice()
            .sort((a, b) => (a.order || 999) - (b.order || 999))
            .map(c => c.id as string);

        if (imported.columnOrder && Array.isArray(imported.columnOrder)) {
            const currentConfigIdsSetOnImport = new Set(allColumnIdsFromConfigSortedOnImport);
            const validStoredOrderOnImport = imported.columnOrder.filter(id => currentConfigIdsSetOnImport.has(id));
            const allCurrentColsInImportedOrder = validStoredOrderOnImport.length === allColumnIdsFromConfigSortedOnImport.length && 
                                                allColumnIdsFromConfigSortedOnImport.every(id => validStoredOrderOnImport.includes(id));
            if (allCurrentColsInImportedOrder) {
                reconciled.columnOrder = validStoredOrderOnImport;
            } else {
                reconciled.columnOrder = allColumnIdsFromConfigSortedOnImport;
                 toast({ title: "列顺序部分重置", description: "导入的列顺序与当前配置不完全匹配，已进行调整。", variant: "default", duration: 7000});
            }
        } else {
            reconciled.columnOrder = allColumnIdsFromConfigSortedOnImport;
        }

        const newVisibility: Record<string, boolean> = {};
        ALL_TASK_COLUMNS_CONFIG.forEach(colDef => {
            if (colDef.isMandatory) newVisibility[colDef.id as string] = true;
            else if (imported.columnVisibility && imported.columnVisibility.hasOwnProperty(colDef.id as string)) {
                newVisibility[colDef.id as string] = imported.columnVisibility[colDef.id as string];
            } else newVisibility[colDef.id as string] = colDef.defaultVisible || false;
        });
         if (newVisibility.completedProgress) {
            newVisibility.requiredVolume = false;
            newVisibility.completedVolume = false;
        } else if (newVisibility.requiredVolume !== false || newVisibility.completedVolume !== false) {
           newVisibility.completedProgress = false;
        }
        reconciled.columnVisibility = newVisibility;

        const newWidths: Record<string, number> = {};
        ALL_TASK_COLUMNS_CONFIG.forEach(colDef => {
            newWidths[colDef.id as string] = (imported.columnWidths && imported.columnWidths[colDef.id as string]) || colDef.defaultWidth || 100;
        });
        reconciled.columnWidths = newWidths;
        
        setSettingsState(reconciled);
        toast({ title: "配置已导入", description: "任务列表样式已加载并应用。" });
      } catch (err) {
        console.error("Failed to import settings:", err);
        toast({ title: "导入失败", description: err instanceof Error ? err.message : "无法解析配置文件。", variant: "destructive" });
      }
    };
    reader.onerror = () => toast({ title: "导入失败", description: "读取文件时发生错误。", variant: "destructive" });
    reader.readAsText(file);
    if (event.target) event.target.value = ''; 
  }, [toast]);

  const triggerImport = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const openColumnVisibilityModal = useCallback(() => setIsColumnVisibilityModalOpen(true), []);
  const closeColumnVisibilityModal = useCallback(() => setIsColumnVisibilityModalOpen(false), []);
  
  const openColumnSpecificStyleModal = useCallback((columnDef: CustomColumnDefinition) => {
    setEditingColumnDef(columnDef);
    setIsColumnSpecificStyleModalOpen(true);
  }, []);
  const closeColumnSpecificStyleModal = useCallback(() => {
    setIsColumnSpecificStyleModalOpen(false);
    setEditingColumnDef(null);
  }, []);

  const openStyleEditorModal = useCallback(() => setIsStyleEditorModalOpen(true), []);
  const closeStyleEditorModal = useCallback(() => setIsStyleEditorModalOpen(false), []);


  return {
    settings,
    isSettingsLoaded,
    updateSetting,
    handleColumnVisibilityChange,
    handleColumnOrderChange,
    handleSingleColumnWidthChange, 
    handleColumnTextStyleChange,
    handleColumnBackgroundChange,
    handleVehiclesPerRowChange,
    handleCustomDispatchVehiclesColumnWidth,
    resetAllSettings,
    exportSettings,
    handleImportFile, 
    triggerImport,
    fileInputRef,

    isColumnVisibilityModalOpen,
    openColumnVisibilityModal,
    closeColumnVisibilityModal,
    isColumnSpecificStyleModalOpen,
    editingColumnDef,
    openColumnSpecificStyleModal,
    closeColumnSpecificStyleModal,

    isStyleEditorModalOpen,
    openStyleEditorModal,
    closeStyleEditorModal,
    
    // 分组相关
    updateGroupConfig,
    handleToggleGrouping,
    handleSetGroupBy,
    handleToggleGroupCollapse,
    isGroupConfigModalOpen,
    setIsGroupConfigModalOpen,
  };
}
