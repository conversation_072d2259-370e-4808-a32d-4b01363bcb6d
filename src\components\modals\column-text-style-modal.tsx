
'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import type { CustomColumnDefinition, StyleableColumnId, ColumnTextStyles } from '@/types';

interface ColumnTextStyleModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  styleableColumns: CustomColumnDefinition[];
  columnTextStyles: ColumnTextStyles;
  onColumnTextStyleChange: (
    columnId: StyleableColumnId,
    styleProperty: keyof NonNullable<ColumnTextStyles[StyleableColumnId]>,
    value: string
  ) => void;
}

const colorOptions = [
  { label: '默认', value: 'default' },
  { label: '主要 (主题蓝)', value: 'primary' },
  { label: '强调 (主题橙)', value: 'accent' },
  { label: '柔和 (灰色)', value: 'muted' },
  { label: '警示 (红色)', value: 'destructive' },
];

const fontSizeOptions = [
  { label: '默认', value: 'default' },
  { label: '小 (12px)', value: 'text-[12px]' },
  { label: '中 (13px)', value: 'text-[13px]' },
  { label: '大 (14px)', value: 'text-[14px]' },
  { label: '较大 (15px)', value: 'text-[15px]' },
  { label: '特大 (16px)', value: 'text-[16px]' },
];

const fontWeightOptions = [
  { label: '默认', value: 'default' },
  { label: '正常', value: 'font-normal' },
  { label: '中等', value: 'font-medium' },
  { label: '半粗', value: 'font-semibold' },
  { label: '粗体', value: 'font-bold' },
];

export function ColumnTextStyleModal({
  isOpen,
  onOpenChange,
  styleableColumns,
  columnTextStyles,
  onColumnTextStyleChange,
}: ColumnTextStyleModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>列文本样式设置</DialogTitle>
          <DialogDescription>
            自定义表格中可设置样式列的文本外观。
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="max-h-[60vh] py-4 pr-4">
          <Accordion type="multiple" className="w-full">
            {styleableColumns.map((col) => (
              <AccordionItem value={col.id as string} key={col.id}>
                <AccordionTrigger>{col.label}</AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4 p-1">
                    <div>
                      <Label htmlFor={`color-${col.id}`} className="text-xs text-muted-foreground">颜色</Label>
                      <Select
                        value={columnTextStyles[col.id as StyleableColumnId]?.color || 'default'}
                        onValueChange={(value) =>
                          onColumnTextStyleChange(col.id as StyleableColumnId, 'color', value)
                        }
                      >
                        <SelectTrigger id={`color-${col.id}`}>
                          <SelectValue placeholder="选择颜色" />
                        </SelectTrigger>
                        <SelectContent>
                          {colorOptions.map((opt) => (
                            <SelectItem key={opt.value} value={opt.value} onSelect={(e) => e.preventDefault()}>
                              {opt.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor={`fontSize-${col.id}`} className="text-xs text-muted-foreground">字号</Label>
                      <Select
                        value={columnTextStyles[col.id as StyleableColumnId]?.fontSize || 'default'}
                        onValueChange={(value) =>
                          onColumnTextStyleChange(col.id as StyleableColumnId, 'fontSize', value)
                        }
                      >
                        <SelectTrigger id={`fontSize-${col.id}`}>
                          <SelectValue placeholder="选择字号" />
                        </SelectTrigger>
                        <SelectContent>
                          {fontSizeOptions.map((opt) => (
                            <SelectItem key={opt.value} value={opt.value} onSelect={(e) => e.preventDefault()}>
                              {opt.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor={`fontWeight-${col.id}`} className="text-xs text-muted-foreground">字重</Label>
                      <Select
                        value={columnTextStyles[col.id as StyleableColumnId]?.fontWeight || 'default'}
                        onValueChange={(value) =>
                          onColumnTextStyleChange(col.id as StyleableColumnId, 'fontWeight', value)
                        }
                      >
                        <SelectTrigger id={`fontWeight-${col.id}`}>
                          <SelectValue placeholder="选择字重" />
                        </SelectTrigger>
                        <SelectContent>
                          {fontWeightOptions.map((opt) => (
                            <SelectItem key={opt.value} value={opt.value} onSelect={(e) => e.preventDefault()}>
                              {opt.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </ScrollArea>
        <DialogFooter>
          <Button type="button" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
