'use client';

import React, { useState, useEffect } from 'react';
import { DataSourceSwitcher } from '@/utils/dataSourceSwitcher';
import { getApiConfig } from '@/config/api';

export function DataSourceStatus() {
  const [configInfo, setConfigInfo] = useState(DataSourceSwitcher.getConfigInfo());
  const [apiConfig, setApiConfig] = useState(getApiConfig());

  useEffect(() => {
    // 定期更新配置信息
    const interval = setInterval(() => {
      setConfigInfo(DataSourceSwitcher.getConfigInfo());
      setApiConfig(getApiConfig());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-lg p-3 text-xs shadow-lg min-w-[200px]">
      <div className="font-semibold text-gray-800 mb-2">数据源状态</div>
      
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          <span className="text-gray-600">模式:</span>
          <span className={`px-2 py-1 rounded text-white text-xs ${
            configInfo.mode === 'mock' ? 'bg-orange-500' : 'bg-green-500'
          }`}>
            {configInfo.mode === 'mock' ? 'Mock数据' : 'API数据'}
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-gray-600">API地址:</span>
          <span className="text-gray-800 font-mono text-xs break-all">
            {apiConfig.baseUrl || 'Mock模式'}
          </span>
        </div>
        
        <div className="text-gray-500 text-xs">
          {configInfo.description}
        </div>
        
        <div className="pt-2 border-t border-gray-200">
          <button
            onClick={() => {
              const newMode = DataSourceSwitcher.toggleMode();
              setConfigInfo(DataSourceSwitcher.getConfigInfo());
              setApiConfig(getApiConfig());
              
              // 显示切换提示
              const message = `已切换到${newMode === 'mock' ? 'Mock数据' : 'API数据'}模式`;
              alert(message + '\n页面将在2秒后刷新以应用新配置');
              
              setTimeout(() => {
                window.location.reload();
              }, 2000);
            }}
            className="w-full px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors"
          >
            切换到{configInfo.mode === 'mock' ? 'API' : 'Mock'}模式
          </button>
        </div>
      </div>
    </div>
  );
}
