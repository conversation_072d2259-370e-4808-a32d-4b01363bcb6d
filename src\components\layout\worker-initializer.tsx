'use client';

import { useEffect, useCallback, useState, useRef } from 'react';
import { useAppStore, handleWorkerMessage } from '@/store/appStore';
import type { ReminderWorkerInput, ReminderWorkerOutput, Task } from '@/types';
import { useToast } from '@/hooks/use-toast';

// 辅助函数：检测任务列表变化
const getChangedTasks = (prevTasks: Task[], currentTasks: Task[]): Task[] => {
  if (!prevTasks || prevTasks.length === 0) {
    return currentTasks;
  }
  
  // 检查任务列表长度是否发生变化
  if (prevTasks.length !== currentTasks.length) {
    return currentTasks;
  }
  
  // 检查重要字段是否发生变化
  const changedTasks = currentTasks.filter((task, index) => {
    const prevTask = prevTasks[index];
    return (
      task.id !== prevTask.id ||
      task.dispatchStatus !== prevTask.dispatchStatus ||
      task.dispatchFrequencyMinutes !== prevTask.dispatchFrequencyMinutes ||
      task.lastDispatchTime !== prevTask.lastDispatchTime ||
      task.defaultDispatchFrequencyMinutes !== prevTask.defaultDispatchFrequencyMinutes
    );
  });
  
  return changedTasks.length > 0 ? currentTasks : [];
};

export function WorkerInitializer() {
  const { initializeTaskManager, cleanupTaskManager, tasks, triggerWorkerUpdate } = useAppStore();
  const { toast } = useToast();
  const [retryCount, setRetryCount] = useState(0);
  const [workerError, setWorkerError] = useState<Error | null>(null);
  const lastTaskUpdateRef = useRef<Task[]>([]);
  
  // 初始化Worker和处理Worker错误
  useEffect(() => {
    // 初始化任务管理器
    initializeTaskManager();
    
    // 强制首次更新
    setTimeout(() => {
      triggerWorkerUpdate();
    }, 1000);
    
    return () => {
      // 清理任务管理器
      cleanupTaskManager();
    };
  }, [initializeTaskManager, cleanupTaskManager, triggerWorkerUpdate]);
  
  // 监听任务变化并更新Worker
  useEffect(() => {
    if (tasks.length === 0) return; // 跳过空任务列表
    
    const changedTasks = getChangedTasks(lastTaskUpdateRef.current, tasks);
    if (changedTasks.length) {
      // 触发Worker更新
      triggerWorkerUpdate();
      lastTaskUpdateRef.current = [...tasks];
    }
  }, [tasks, triggerWorkerUpdate]);
  
  // 处理Worker错误
  useEffect(() => {
    if (workerError) {
      console.error("Reminder Worker Error:", workerError);
      toast({
        title: "提醒服务错误",
        description: "后台提醒服务遇到问题，部分功能可能受限。",
        variant: "destructive",
      });
      
      // 实现错误重试
      const delay = Math.min(1000 * 2 ** retryCount, 30000);
      const timer = setTimeout(() => {
        triggerWorkerUpdate();
        setRetryCount((c) => c + 1);
      }, delay);
      
      return () => clearTimeout(timer);
    }
  }, [workerError, toast, retryCount, triggerWorkerUpdate]);

  return null; // 此组件不渲染任何UI
}
