# React Beautiful DND 集成示例

## 如何在现有项目中集成新的拖拽系统

### 1. 在主任务列表组件中集成

```typescript
// src/components/sections/task-list/task-list.tsx

import { EnhancedTaskListView } from './EnhancedTaskListView';

export const TaskList = () => {
  // ... 现有的状态和逻辑

  return (
    <div className="task-list-container">
      {/* 其他组件 */}
      
      {/* 在list模式中使用增强的任务列表 */}
      {viewMode === 'list' && (
        <EnhancedTaskListView
          filteredTasks={filteredTasks}
          vehicles={vehicles}
          settings={settings}
          productionLineCount={productionLineCount}
          vehicleDisplayMode={vehicleDisplayMode}
          taskStatusFilter={taskStatusFilter}
          onCancelVehicleDispatch={handleCancelVehicleDispatch}
          onOpenDeliveryOrderDetailsForVehicle={handleOpenDeliveryOrderDetails}
          onOpenVehicleCardContextMenu={handleVehicleContextMenu}
          onTaskContextMenu={handleTaskContextMenu}
          onTaskDoubleClick={handleTaskDoubleClick}
          onOpenStyleEditor={handleOpenStyleEditor}
        />
      )}
      
      {/* table模式保持不变 */}
      {viewMode === 'table' && (
        <TaskListTableView
          // ... 现有的table模式属性
        />
      )}
    </div>
  );
};
```

### 2. 在应用根组件中添加DragDropProvider

```typescript
// src/app/layout.tsx 或 src/components/layout/AppLayout.tsx

import { DragDropProvider } from '@/contexts/DragDropContext';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh">
      <body>
        <DragDropProvider>
          {children}
        </DragDropProvider>
      </body>
    </html>
  );
}
```

### 3. 渐进式迁移策略

#### 阶段1：并行运行
```typescript
// 添加一个开关来控制使用哪个版本
const [useEnhancedDragDrop, setUseEnhancedDragDrop] = useState(false);

return (
  <div>
    {/* 开关控制 */}
    <div className="mb-4">
      <label className="flex items-center gap-2">
        <input
          type="checkbox"
          checked={useEnhancedDragDrop}
          onChange={(e) => setUseEnhancedDragDrop(e.target.checked)}
        />
        使用增强拖拽系统 (Beta)
      </label>
    </div>

    {/* 条件渲染 */}
    {viewMode === 'list' && (
      useEnhancedDragDrop ? (
        <EnhancedTaskListView {...props} />
      ) : (
        <TaskListListView {...props} />
      )
    )}
  </div>
);
```

#### 阶段2：功能对比
```typescript
// 添加性能监控和用户反馈
const [dragDropMetrics, setDragDropMetrics] = useState({
  oldSystem: { operations: 0, errors: 0, avgTime: 0 },
  newSystem: { operations: 0, errors: 0, avgTime: 0 }
});

// 在拖拽操作中收集指标
const handleDragOperation = async (system: 'old' | 'new', operation: () => Promise<void>) => {
  const startTime = performance.now();
  try {
    await operation();
    const endTime = performance.now();
    // 更新成功指标
    setDragDropMetrics(prev => ({
      ...prev,
      [system]: {
        ...prev[system],
        operations: prev[system].operations + 1,
        avgTime: (prev[system].avgTime + (endTime - startTime)) / 2
      }
    }));
  } catch (error) {
    // 更新错误指标
    setDragDropMetrics(prev => ({
      ...prev,
      [system]: {
        ...prev[system],
        errors: prev[system].errors + 1
      }
    }));
    throw error;
  }
};
```

#### 阶段3：完全替换
```typescript
// 移除旧系统，完全使用新系统
{viewMode === 'list' && (
  <EnhancedTaskListView {...props} />
)}
```

### 4. 配置迁移

#### 保持现有配置兼容
```typescript
// 确保新系统使用现有的配置
const enhancedProps = {
  ...existingProps,
  settings: {
    ...settings,
    // 新系统特有的配置
    dragDropAnimations: settings.dragDropAnimations ?? true,
    dragDropSounds: settings.dragDropSounds ?? false,
    dragDropHaptics: settings.dragDropHaptics ?? true,
  }
};
```

#### 配置升级
```typescript
// 自动升级旧配置到新格式
const upgradeSettings = (oldSettings: any) => {
  return {
    ...oldSettings,
    // 迁移拖拽相关配置
    dragDropConfig: {
      enableAnimations: oldSettings.enableDragAnimations ?? true,
      enableSounds: oldSettings.enableDragSounds ?? false,
      animationDuration: oldSettings.dragAnimationDuration ?? 200,
      // 新增配置
      enableHapticFeedback: true,
      enableVisualFeedback: true,
    }
  };
};
```

### 5. 测试策略

#### 单元测试
```typescript
// src/components/sections/task-list/__tests__/DragDropContext.test.tsx

import { render, screen } from '@testing-library/react';
import { DragDropProvider } from '../DragDropContext';
import { EnhancedTaskListView } from '../EnhancedTaskListView';

describe('DragDropContext', () => {
  it('should provide drag drop context to children', () => {
    render(
      <DragDropProvider>
        <EnhancedTaskListView {...mockProps} />
      </DragDropProvider>
    );
    
    expect(screen.getByText('车辆调度面板')).toBeInTheDocument();
  });

  it('should handle drag operations correctly', async () => {
    // 测试拖拽操作
  });
});
```

#### 集成测试
```typescript
// src/components/sections/task-list/__tests__/DragDrop.integration.test.tsx

import { render, fireEvent, waitFor } from '@testing-library/react';
import { DragDropProvider } from '../DragDropContext';
import { EnhancedTaskListView } from '../EnhancedTaskListView';

describe('Drag and Drop Integration', () => {
  it('should dispatch vehicle to task on drag and drop', async () => {
    const mockDispatch = jest.fn();
    
    render(
      <DragDropProvider>
        <EnhancedTaskListView
          {...mockProps}
          onDispatchVehicle={mockDispatch}
        />
      </DragDropProvider>
    );

    // 模拟拖拽操作
    const vehicle = screen.getByTestId('vehicle-card-1');
    const task = screen.getByTestId('task-card-1');

    fireEvent.dragStart(vehicle);
    fireEvent.dragOver(task);
    fireEvent.drop(task);

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith('vehicle-1', 'task-1');
    });
  });
});
```

#### E2E测试
```typescript
// cypress/integration/drag-drop.spec.ts

describe('Vehicle Drag and Drop', () => {
  beforeEach(() => {
    cy.visit('/task-list');
    cy.get('[data-testid="view-mode-list"]').click();
  });

  it('should drag vehicle from panel to task', () => {
    // 拖拽车辆到任务
    cy.get('[data-testid="vehicle-card-1"]')
      .trigger('dragstart');
    
    cy.get('[data-testid="task-card-1"]')
      .trigger('dragover')
      .trigger('drop');

    // 验证结果
    cy.get('[data-testid="task-card-1"]')
      .should('contain', '车辆-1');
    
    cy.get('.toast')
      .should('contain', '车辆分配成功');
  });

  it('should reorder vehicles in list', () => {
    // 测试车辆重排序
    cy.get('[data-testid="vehicle-card-1"]')
      .trigger('dragstart');
    
    cy.get('[data-testid="vehicle-card-3"]')
      .trigger('dragover')
      .trigger('drop');

    // 验证排序结果
    cy.get('[data-testid="vehicle-list"]')
      .children()
      .first()
      .should('contain', '车辆-1');
  });
});
```

### 6. 性能监控

#### 拖拽性能指标
```typescript
// src/hooks/useDragDropMetrics.ts

export const useDragDropMetrics = () => {
  const [metrics, setMetrics] = useState({
    dragStartTime: 0,
    dragDuration: 0,
    dropSuccess: 0,
    dropFailure: 0,
    averageResponseTime: 0,
  });

  const trackDragStart = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      dragStartTime: performance.now()
    }));
  }, []);

  const trackDragEnd = useCallback((success: boolean) => {
    const endTime = performance.now();
    setMetrics(prev => {
      const duration = endTime - prev.dragStartTime;
      return {
        ...prev,
        dragDuration: duration,
        dropSuccess: success ? prev.dropSuccess + 1 : prev.dropSuccess,
        dropFailure: success ? prev.dropFailure : prev.dropFailure + 1,
        averageResponseTime: (prev.averageResponseTime + duration) / 2,
      };
    });
  }, []);

  return { metrics, trackDragStart, trackDragEnd };
};
```

#### 内存使用监控
```typescript
// src/hooks/useMemoryMonitor.ts

export const useMemoryMonitor = () => {
  const [memoryUsage, setMemoryUsage] = useState<number>(0);

  useEffect(() => {
    const monitor = setInterval(() => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMemoryUsage(memory.usedJSHeapSize / 1024 / 1024); // MB
      }
    }, 1000);

    return () => clearInterval(monitor);
  }, []);

  return memoryUsage;
};
```

### 7. 用户反馈收集

#### 用户体验调研
```typescript
// src/components/feedback/DragDropFeedback.tsx

export const DragDropFeedback = () => {
  const [feedback, setFeedback] = useState({
    easeOfUse: 0,
    visualFeedback: 0,
    performance: 0,
    suggestions: ''
  });

  const submitFeedback = async () => {
    await fetch('/api/feedback/drag-drop', {
      method: 'POST',
      body: JSON.stringify(feedback)
    });
  };

  return (
    <div className="feedback-form">
      <h3>拖拽系统体验反馈</h3>
      
      <div className="rating-group">
        <label>易用性 (1-5星)</label>
        <StarRating 
          value={feedback.easeOfUse}
          onChange={(value) => setFeedback(prev => ({ ...prev, easeOfUse: value }))}
        />
      </div>

      <div className="rating-group">
        <label>视觉反馈 (1-5星)</label>
        <StarRating 
          value={feedback.visualFeedback}
          onChange={(value) => setFeedback(prev => ({ ...prev, visualFeedback: value }))}
        />
      </div>

      <div className="rating-group">
        <label>性能表现 (1-5星)</label>
        <StarRating 
          value={feedback.performance}
          onChange={(value) => setFeedback(prev => ({ ...prev, performance: value }))}
        />
      </div>

      <textarea
        placeholder="其他建议..."
        value={feedback.suggestions}
        onChange={(e) => setFeedback(prev => ({ ...prev, suggestions: e.target.value }))}
      />

      <button onClick={submitFeedback}>提交反馈</button>
    </div>
  );
};
```

## 总结

通过以上集成步骤，您可以：

1. **🔄 渐进式迁移**：从并行运行到完全替换
2. **📊 性能监控**：实时监控拖拽性能和用户体验
3. **🧪 全面测试**：单元测试、集成测试、E2E测试
4. **📈 数据驱动**：收集用户反馈和使用数据
5. **🛡️ 风险控制**：保持向后兼容，降低迁移风险

新的 react-beautiful-dnd 拖拽系统将为您的应用带来更好的用户体验和更强的功能性。
