// src/contexts/TaskRowHighlightContext.tsx
'use client';

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { useTaskRowHighlight } from '@/hooks/useTaskRowHighlight';

interface TaskRowHighlightContextType {
  highlightedTaskId: string | null;
  setHighlightedTaskId: (taskId: string | null) => void;
}

const TaskRowHighlightContext = createContext<TaskRowHighlightContextType | undefined>(undefined);

interface TaskRowHighlightProviderProps {
  children: React.ReactNode;
}

export const TaskRowHighlightProvider: React.FC<TaskRowHighlightProviderProps> = ({ children }) => {
  const [highlightedTaskId, setHighlightedTaskId] = useState<string | null>(null);

  // Use the task row highlight hook
  useTaskRowHighlight({ highlightedTaskId });

  // 全局监听拖拽结束事件，确保清除高亮
  useEffect(() => {
    const handleDragEnd = () => {
      setHighlightedTaskId(null);
    };

    const handleMouseUp = () => {
      // 延迟清除，给drop事件时间完成
      setTimeout(() => {
        setHighlightedTaskId(null);
      }, 100);
    };

    document.addEventListener('dragend', handleDragEnd);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('dragend', handleDragEnd);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, []);

  const contextValue: TaskRowHighlightContextType = {
    highlightedTaskId,
    setHighlightedTaskId,
  };

  return (
    <TaskRowHighlightContext.Provider value={contextValue}>
      {children}
    </TaskRowHighlightContext.Provider>
  );
};

export const useTaskRowHighlightContext = (): TaskRowHighlightContextType => {
  const context = useContext(TaskRowHighlightContext);
  if (!context) {
    throw new Error('useTaskRowHighlightContext must be used within a TaskRowHighlightProvider');
  }
  return context;
};

// Simplified hook for setting task highlight
export const useTaskHighlight = () => {
  const context = useTaskRowHighlightContext();

  return {
    highlightedTaskId: context.highlightedTaskId,
    setHighlightedTaskId: context.setHighlightedTaskId,
  };
};
