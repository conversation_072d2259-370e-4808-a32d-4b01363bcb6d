
import type { LucideIcon } from 'lucide-react';
import { Button, type ButtonProps } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface IconButtonProps extends Omit<ButtonProps, 'size'> {
  icon: LucideIcon;
  tooltipLabel: string;
}

export function IconButton({ icon: Icon, tooltipLabel, className, ...props }: IconButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {/* Using explicit padding and dimensions for a custom small icon button */}
          <Button variant="ghost" className={`h-7 w-7 p-1 aspect-square ${className}`} {...props}>
            <Icon className="h-3.5 w-3.5" />
            <span className="sr-only">{tooltipLabel}</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltipLabel}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

