@tailwind base;
@tailwind components;
@tailwind utilities;

/* 添加一个淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* 列宽调整样式增强 */
.cursor-col-resize:hover {
  background-color: hsl(var(--primary) / 50%) !important;
  opacity: 0.7;
}

/* 固定列的列宽调整样式 */
th[class*="sticky"] .cursor-col-resize {
  z-index: 50 !important;
}

/* 自定义细滚动条 */
.custom-thin-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted) / 70%) transparent;
}

.custom-thin-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.custom-thin-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-thin-scrollbar::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted) / 70%);
  border-radius: 6px;
}

.custom-thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted) / 90%);
}

/* 固定列样式已迁移到 sticky-columns.css 文件中统一管理 */
@import './sticky-columns.css';

/* 移除了 sticky-col-shadow-main 相关样式，统一使用 box-shadow 实现 */

/* productionLines 列的固定样式现在由 virtualized-table.tsx 统一管理 */

/* 调度车辆列不再 sticky，无需 sticky 样式 */

/* 调度车辆列宽度调整手柄在左侧 */
th[data-column-id="dispatchedVehicles"] .resize-handle-dispatch-vehicles {
  left: 0;
  right: auto;
  width: 8px;
  min-width: 8px;
  max-width: 8px;
  height: 100%;
  z-index: 999;
  cursor: col-resize;
  position: absolute;
  background: transparent;
  pointer-events: auto;
}

th[data-column-id="dispatchedVehicles"] .resize-handle-dispatch-vehicles::after {
  content: '';
  display: block;
  position: absolute;
  left: 50%;
  top: 12%;
  width: 1px;
  height: 76%;
  background: transparent;
  transition: background 0.15s;
  transform: translateX(-50%);
}

th[data-column-id="dispatchedVehicles"] .resize-handle-dispatch-vehicles:hover::after {
  background: #a3a3a3;
}

th[data-column-id="dispatchedVehicles"] .truncate {
  padding-left: 14px;
}

/* 调度车辆卡片尺寸强制样式 */
.w-8 {
  width: 32px !important;
}
.w-9 {
  width: 36px !important;
}
.w-12 {
  width: 48px !important;
}
.w-14 {
  width: 56px !important;
}
.w-16 {
  width: 64px !important;
}
.w-20 {
  width: 80px !important;
}
.w-24 {
  width: 96px !important;
}

.h-7 {
  height: 28px !important;
}
.h-8 {
  height: 32px !important;
}
.h-9 {
  height: 36px !important;
}
.h-10 {
  height: 40px !important;
}

/* 虚拟化表格滚动优化 */
.virtualized-table-container {
  height: 100%;
  min-height: 0;
  flex: 1 1 0%;
  overflow: auto;
  position: relative;
  contain: layout style paint;
}

/* 确保表格容器正确处理滚动 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--border)) hsl(var(--background));
  scroll-behavior: smooth;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--background));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
  transition: background 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--accent));
}

/* 修复表格行高度和滚动性能 */
.virtualized-table {
  table-layout: fixed;
  width: 100%;
  border-collapse: collapse;
}

.virtualized-table tbody tr {
  will-change: transform;
}

.virtualized-table tbody tr {
  border-bottom: 1px solid #e5e7eb;
  border-radius: 0;
  transition: background 0.15s;
}

.virtualized-table tbody tr:last-child {
  border-bottom: none;
}

.virtualized-table thead th {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
  font-size: 11px !important;
  height: 28px !important;
  min-height: 0 !important;
  max-height: 28px !important;
  line-height: 1.1 !important;
}

/* 表格分割线优化：每个单元格底部分割线，最后一行无分割线 */
.virtualized-table tbody td {
  border-bottom: 1px solid hsl(var(--border));
  background-clip: padding-box;
}

.virtualized-table tbody tr:last-child td {
  border-bottom: none;
}