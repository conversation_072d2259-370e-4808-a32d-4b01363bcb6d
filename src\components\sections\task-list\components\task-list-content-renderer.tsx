// src/components/sections/task-list/components/task-list-content-renderer.tsx

import React from 'react';
import type { ColumnDef } from '@tanstack/react-table';
import { cn } from '@/lib/utils';
import type { 
  Task, 
  Vehicle, 
  DensityStyleValues, 
  TaskListStoredSettings, 
  VehicleDisplayMode,
  TaskGroup,
  CustomColumnDefinition
} from '@/types';
import { TaskCardConfig } from '@/types/taskCardConfig';
import { TaskListTableContent } from './task-list-table-content';
import { TaskListCardContent } from './task-list-card-content';

interface TaskListContentRendererProps {
  // Display mode
  displayMode: 'table' | 'card';
  
  // Data
  filteredTasks: Task[];
  tasksWithVehicles: Task[];
  taskGroups: TaskGroup[];
  allVehicles: Vehicle[];
  tableColumns: ColumnDef<Task>[];
  
  // Settings
  settings: TaskListStoredSettings;
  densityStyles: DensityStyleValues;
  vehicleDisplayMode: VehicleDisplayMode;
  taskStatusFilter: string;
  taskCardConfig: TaskCardConfig;
  
  // Dimensions and calculations
  tableTotalWidth: number;
  estimateRowHeight: (task?: Task) => number;
  cardGridContainerClasses: string;
  
  // Drag and Drop State
  dragOverTaskId: string | null;
  setDragOverTaskId: (taskId: string | null) => void;
  dragOverProductionLineId: string | null;
  setDragOverProductionLineId: (lineId: string | null) => void;
  
  // Event Handlers - Table
  onColumnSizingChange: (sizing: any) => void;
  onColumnOrderChange: (newOrder: string[]) => void;
  onColumnVisibilityChange: (visibility: any) => void;
  onHeaderContextMenu: (event: React.MouseEvent, columnDef: CustomColumnDefinition) => void;
  onHeaderDoubleClick: (event: React.MouseEvent, columnDef: CustomColumnDefinition) => void;
  onRowContextMenu: (e: React.MouseEvent, row: any) => void;
  onRowDoubleClick: (row: any) => void;
  
  // Event Handlers - General
  onDropOnProductionLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onToggleGroupCollapse: (groupKey: string) => void;
  onCancelGrouping: () => void;
  onTaskContextMenu: (event: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick: (task: Task) => void;
  
  // Event Handlers - Vehicle/Card
  onVehicleDrop: (vehicle: Vehicle, taskId: string) => void;
  onOpenVehicleCardContextMenu: (e: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  onOpenDeliveryOrderDetailsForVehicle: (vehicleId: string, taskId: string) => void;
  onOpenStyleEditor: () => void;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  onVehicleDispatchedToLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onDropVehicleFromPanelOnTaskCard: (vehicle: Vehicle, taskId: string) => void;
  onTaskCardConfigChange: (config: TaskCardConfig) => void;
  onOpenCardConfigModal: () => void;
  
  // Styling
  getColumnBackgroundProps: (columnId: string, isHeader: boolean, isFixed: boolean) => { style: React.CSSProperties, className: string };
  getStatusLabelProps: (status: string) => { label: string; variant: string };
}

export function TaskListContentRenderer({
  displayMode,
  filteredTasks,
  tasksWithVehicles,
  taskGroups,
  allVehicles,
  tableColumns,
  settings,
  densityStyles,
  vehicleDisplayMode,
  taskStatusFilter,
  taskCardConfig,
  tableTotalWidth,
  estimateRowHeight,
  cardGridContainerClasses,
  dragOverTaskId,
  setDragOverTaskId,
  dragOverProductionLineId,
  setDragOverProductionLineId,
  onColumnSizingChange,
  onColumnOrderChange,
  onColumnVisibilityChange,
  onHeaderContextMenu,
  onHeaderDoubleClick,
  onRowContextMenu,
  onRowDoubleClick,
  onDropOnProductionLine,
  onToggleGroupCollapse,
  onCancelGrouping,
  onTaskContextMenu,
  onTaskDoubleClick,
  onVehicleDrop,
  onOpenVehicleCardContextMenu,
  onOpenDeliveryOrderDetailsForVehicle,
  onOpenStyleEditor,
  onCancelVehicleDispatch,
  onVehicleDispatchedToLine,
  onDropVehicleFromPanelOnTaskCard,
  onTaskCardConfigChange,
  onOpenCardConfigModal,
  getColumnBackgroundProps,
  getStatusLabelProps,
}: TaskListContentRendererProps) {
  
  if (displayMode === 'table') {
    return (
      <TaskListTableContent
        tasks={tasksWithVehicles}
        taskGroups={taskGroups}
        tableColumns={tableColumns}
        enableZebraStriping={settings.enableZebraStriping}
        densityStyles={densityStyles}
        groupConfig={settings.groupConfig}
        tableTotalWidth={tableTotalWidth}
        estimateRowHeight={estimateRowHeight}
        columnSizing={settings.columnWidths}
        columnVisibility={settings.columnVisibility}
        columnOrder={settings.columnOrder}
        onColumnSizingChange={onColumnSizingChange}
        onColumnOrderChange={(updater) => {
          if (typeof updater === 'function') {
            const newOrder = updater([]);
            onColumnOrderChange(newOrder);
          } else {
            onColumnOrderChange(updater);
          }
        }}
        onColumnVisibilityChange={onColumnVisibilityChange}
        onHeaderContextMenu={onHeaderContextMenu}
        onHeaderDoubleClick={onHeaderDoubleClick}
        onRowContextMenu={onRowContextMenu}
        onRowDoubleClick={onRowDoubleClick}
        onDropOnProductionLine={onDropOnProductionLine}
        onToggleGroupCollapse={onToggleGroupCollapse}
        onCancelGrouping={onCancelGrouping}
        getColumnBackgroundProps={getColumnBackgroundProps}
      />
    );
  }

  if (displayMode === 'card') {
    return (
      <div className={cardGridContainerClasses}>
        <TaskListCardContent
          filteredTasks={filteredTasks}
          vehicles={allVehicles}
          taskGroups={taskGroups}
          settings={settings}
          vehicleDisplayMode={vehicleDisplayMode}
          taskStatusFilter={taskStatusFilter}
          taskCardConfig={taskCardConfig}
          dragOverTaskId={dragOverTaskId}
          setDragOverTaskId={setDragOverTaskId}
          dragOverProductionLineId={dragOverProductionLineId}
          setDragOverProductionLineId={setDragOverProductionLineId}
          handleVehicleDrop={onVehicleDrop}
          handleTaskContextMenu={onTaskContextMenu}
          handleRowDoubleClick={onTaskDoubleClick}
          onOpenVehicleCardContextMenu={onOpenVehicleCardContextMenu}
          onOpenDeliveryOrderDetailsForVehicle={onOpenDeliveryOrderDetailsForVehicle}
          onOpenStyleEditor={onOpenStyleEditor}
          onCancelVehicleDispatch={onCancelVehicleDispatch}
          onVehicleDispatchedToLine={onVehicleDispatchedToLine}
          onDropVehicleFromPanelOnTaskCard={onDropVehicleFromPanelOnTaskCard}
          onDropVehicleOnLine={onDropOnProductionLine}
          onToggleGroupCollapse={onToggleGroupCollapse}
          onCancelGrouping={onCancelGrouping}
          onTaskCardConfigChange={onTaskCardConfigChange}
          onOpenCardConfigModal={onOpenCardConfigModal}
          getStatusLabelProps={getStatusLabelProps}
        />
      </div>
    );
  }

  return null;
}
