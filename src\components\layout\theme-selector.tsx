
'use client';

import { Palette } from 'lucide-react';
import { useTheme, THEME_OPTIONS, type Theme } from '@/contexts/theme-provider';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { IconButton } from '../shared/icon-button';

export function ThemeSelector() {
  const { setTheme, theme: currentTheme } = useTheme();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <IconButton icon={Palette} tooltipLabel="切换主题" />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>选择主题</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {THEME_OPTIONS.map((option) => (
          <DropdownMenuItem 
            key={option.value} 
            onClick={() => setTheme(option.value as Theme)} // Cast needed if Theme type is strict
            className={option.value === currentTheme ? 'bg-accent text-accent-foreground focus:bg-accent focus:text-accent-foreground' : ''}
          >
            {option.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
