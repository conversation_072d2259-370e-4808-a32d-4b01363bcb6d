
import type {<PERSON>ada<PERSON>} from 'next';
import { <PERSON>eist } from 'next/font/google';
import './globals.css';
import { Toaster } from "@/components/ui/toaster";
import { ThemeProvider } from '@/contexts/theme-provider';
import { DndContextProvider } from '@/components/layout/dnd-context-provider'; // Import the client wrapper
import { QueryProvider } from '@/components/layout/query-provider';
import localFont from "next/font/local";
import { DevToolsClientWrapper } from '@/components/layout/dev-tools-client-wrapper';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
  display: 'swap',
});

/**
 * PingFang SC 字体配置 - 用于等宽字体显示
 * 使用本地字体文件避免网络连接问题
 */
const pingFangMono = localFont({
  src: [
    {
      path: '../assets/fonts/ttf/PingFangSC-Light.ttf',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../assets/fonts/ttf/PingFangSC-Regular.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../assets/fonts/ttf/PingFangSC-Medium.ttf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../assets/fonts/ttf/PingFangSC-Semibold.ttf',
      weight: '600',
      style: 'normal',
    },
  ],
  variable: '--font-pingfang-mono',
  display: 'swap',
});



export const metadata: Metadata = {
  title: 'FEILING',
  description: 'FEILING商砼搅拌站调度管理系统',
  icons: {
    icon:'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNzUwMzgxMzQ5MDExIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjQ2NTkiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTQ3NS4yIDEyOEM1MjkuNiAxMjggNTc2IDE2Ni40IDU3NiAyMjRWMjU2aDIyNGM0OS42IDAgOTYgNDAgOTYgOTZ2MjU3LjZjMCAxNy42LTggMzUuMi0yMC44IDQ2LjRsLTMuMiAzLjItNDAgMjcuMlY4MzJoOTZjMTcuNiAwIDMyIDE0LjQgMzIgMzJzLTEyLjggMzAuNC0zMC40IDMySDk2YTMyLjA2NCAzMi4wNjQgMCAwIDEtMzItMzJjMC0xNy42IDEyLjgtMzAuNCAzMC40LTMySDE5MnYtMTg3LjJsLTQxLjYtMzMuNmMtMTIuOC0xMS4yLTIyLjQtMjcuMi0yMi40LTQ0LjhWMjI0QzEyOCAxNzIuOCAxNjkuNiAxMjggMjI0IDEyOGgyNTEuMnpNNTE4LjQgNjM4LjRsLTYuNCA2LjRWODMyaDY0di0xNDUuNmwtNDEuNi0yOC44YTY0IDY0IDAgMCAxLTE2LTE5LjJ6TTc2OCA3MjkuNmwtNDQuOCAzMC40YTMxLjI5NiAzMS4yOTYgMCAwIDEtMzguNCAxLjZsLTEuNi0xLjYtNDMuMi0yOC44VjgzMmgxMjh2LTEwMi40eiBtLTMyMC0zMmwtNzYuOCA2NGEzMS4yOTYgMzEuMjk2IDAgMCAxLTM4LjQgMS42bC0xLjYtMS42TDI1NiA2OTcuNlY4MzJoMTkydi0xMzQuNHpNNDgwIDE5MmgtMjU2Yy0xNy42IDAtMzIgMTQuNC0zMiAzMnYzMzcuNmwxNjAgMTMyLjhMNTEyIDU2MS42VjIyNGMwLTE3LjYtMTQuNC0zMi0zMi0zMnogbTMyMCAxMjhINTc2djI4OS42bDEyOCA4NC44IDEyOC04NC44VjM1MmMwLTE3LjYtMTQuNC0zMi0zMi0zMnogbS00OCAxOTJjMTcuNiAwIDMyIDE0LjQgMzIgMzJzLTEyLjggMzAuNC0zMC40IDMySDY1NmEzMi4wNjQgMzIuMDY0IDAgMCAxLTMyLTMyYzAtMTcuNiAxMi44LTMwLjQgMzAuNC0zMmg5Ny42eiBtMC0xMjhjMTcuNiAwIDMyIDE0LjQgMzIgMzJzLTEyLjggMzAuNC0zMC40IDMySDY1NmEzMi4wNjQgMzIuMDY0IDAgMCAxLTMyLTMyYzAtMTcuNiAxMi44LTMwLjQgMzAuNC0zMmg5Ny42eiIgZmlsbD0iIzBEQzZGMSIgcC1pZD0iNDY2MCI+PC9wYXRoPjwvc3ZnPg=='
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${geistSans.variable} ${pingFangMono.variable} text-xs font-normal antialiased`}>
        <ThemeProvider
          defaultTheme="oceanic-deep"
          storageKey="app-theme"
        >
          <QueryProvider>
            <DndContextProvider> {/* Use the client wrapper here */}
              {children}
              <Toaster />
              <audio id="dispatch-alert-sound" src="/alert.mp3" preload="auto"></audio>
              <DevToolsClientWrapper />
            </DndContextProvider>
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
