// src/components/test/TaskCardSelectionTest.tsx
'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useTaskSelectionState, useTaskSelectionActions, TaskSelectionProvider } from '@/contexts/TaskSelectionContext';
import { cn } from '@/lib/utils';

const TaskCardSelectionTestContent: React.FC = () => {
  const { selectedTask, selectedTaskId, isTaskSelected } = useTaskSelectionState();
  const { setSelectedTask, clearSelection } = useTaskSelectionActions();

  const testTasks = [
    { 
      id: 'task-1', 
      taskNumber: 'T001', 
      name: '任务 1', 
      dispatchStatus: 'InProgress' as const,
      projectName: '测试项目A',
      strength: 'C30'
    },
    { 
      id: 'task-2', 
      taskNumber: 'T002', 
      name: '任务 2', 
      dispatchStatus: 'Pending' as const,
      projectName: '测试项目B',
      strength: 'C25'
    },
    { 
      id: 'task-3', 
      taskNumber: 'T003', 
      name: '任务 3', 
      dispatchStatus: 'InProgress' as const,
      projectName: '测试项目C',
      strength: 'C35'
    },
  ];

  const handleCardClick = (task: any, e: React.MouseEvent) => {
    // 避免在点击按钮时触发卡片选中
    const target = e.target as HTMLElement;
    if (target.closest('button, a, input, select, textarea, [role="button"]')) {
      return;
    }
    
    // 切换选中状态
    const isCurrentlySelected = isTaskSelected(task.id);
    if (isCurrentlySelected) {
      setSelectedTask(null);
    } else {
      setSelectedTask(task);
    }
  };

  // 模拟操作栏按钮状态
  const hasSelectedTask = !!selectedTaskId;
  const selectedTaskStatus = selectedTask?.dispatchStatus;

  return (
    <div className="p-4 space-y-4">
      <h3 className="text-lg font-semibold">任务卡片选中功能测试</h3>
      
      <div className="bg-blue-50 p-3 rounded border">
        <p className="text-sm">
          <strong>测试说明:</strong> 
          1. 点击任务卡片可以选中任务，再次点击取消选中<br/>
          2. 观察操作栏按钮状态是否正确响应选中状态<br/>
          3. 选中状态应该使用accent颜色（与拖拽高亮的primary颜色不同）
        </p>
      </div>

      {/* 选中状态指示器 */}
      <div className="space-y-2">
        <h4 className="font-medium">当前状态:</h4>
        {hasSelectedTask ? (
          <div className="px-3 py-1 text-xs bg-primary text-primary-foreground rounded border flex items-center gap-2 w-fit">
            <div className="w-2 h-2 bg-primary-foreground rounded-full"></div>
            已选中: {selectedTask?.taskNumber}
            <span className="text-primary-foreground/70">({selectedTask?.dispatchStatus})</span>
          </div>
        ) : (
          <div className="px-3 py-1 text-xs bg-muted text-muted-foreground rounded border flex items-center gap-2 w-fit">
            <div className="w-2 h-2 bg-muted-foreground/50 rounded-full"></div>
            未选中任务
          </div>
        )}
      </div>

      {/* 模拟操作栏按钮 */}
      <div className="space-y-2">
        <h4 className="font-medium">操作栏按钮状态:</h4>
        <div className="flex gap-2 flex-wrap">
          <Button 
            size="sm"
            disabled={!hasSelectedTask}
            variant={hasSelectedTask ? "default" : "secondary"}
          >
            准备生产 {hasSelectedTask && `(${selectedTask?.taskNumber})`}
          </Button>
          <Button 
            size="sm"
            disabled={!hasSelectedTask}
            variant={hasSelectedTask ? "default" : "secondary"}
          >
            转到正在进行 {hasSelectedTask && `(${selectedTask?.taskNumber})`}
          </Button>
          <Button 
            size="sm"
            disabled={!hasSelectedTask || selectedTaskStatus !== 'InProgress'}
            variant={hasSelectedTask && selectedTaskStatus === 'InProgress' ? "default" : "secondary"}
          >
            暂停任务 {hasSelectedTask && `(${selectedTask?.taskNumber})`}
          </Button>
          <Button 
            size="sm"
            disabled={!hasSelectedTask}
            variant={hasSelectedTask ? "destructive" : "secondary"}
          >
            撤销任务 {hasSelectedTask && `(${selectedTask?.taskNumber})`}
          </Button>
        </div>
      </div>

      {/* 任务卡片 */}
      <div className="space-y-2">
        <h4 className="font-medium">任务卡片:</h4>
        <div className="grid grid-cols-3 gap-4">
          {testTasks.map(task => {
            const isSelected = isTaskSelected(task.id);
            
            return (
              <Card
                key={task.id}
                data-task-id={task.id}
                onClick={(e) => handleCardClick(task, e)}
                className={cn(
                  "p-4 cursor-pointer transition-all duration-150 ease-in-out task-card task-card-clickable",
                  isSelected && "task-row-selected"
                )}
              >
                <div className="space-y-2">
                  <div className="flex justify-between items-start">
                    <div className="font-medium text-sm">{task.taskNumber}</div>
                    <div className={cn(
                      "text-xs px-2 py-1 rounded",
                      task.dispatchStatus === 'InProgress' 
                        ? "bg-green-100 text-green-700" 
                        : "bg-yellow-100 text-yellow-700"
                    )}>
                      {task.dispatchStatus === 'InProgress' ? '进行中' : '待开始'}
                    </div>
                  </div>
                  <div className="text-xs text-gray-600">{task.projectName}</div>
                  <div className="text-xs text-gray-500">强度: {task.strength}</div>
                  
                  {/* 测试按钮 - 点击这个不应该触发选中 */}
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="w-full"
                    onClick={(e) => {
                      e.stopPropagation();
                      alert(`点击了 ${task.taskNumber} 的按钮`);
                    }}
                  >
                    测试按钮
                  </Button>
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      {/* 手动控制按钮 */}
      <div className="space-y-2">
        <h4 className="font-medium">手动控制:</h4>
        <div className="flex gap-2">
          {testTasks.map(task => (
            <Button
              key={task.id}
              size="sm"
              variant="outline"
              onClick={() => setSelectedTask(task as any)}
            >
              选中 {task.taskNumber}
            </Button>
          ))}
          <Button
            size="sm"
            variant="outline"
            onClick={clearSelection}
          >
            清除选中
          </Button>
        </div>
      </div>
    </div>
  );
};

// 包装器组件，提供必要的Context
export const TaskCardSelectionTest: React.FC = () => {
  return (
    <TaskSelectionProvider>
      <TaskCardSelectionTestContent />
    </TaskSelectionProvider>
  );
};
