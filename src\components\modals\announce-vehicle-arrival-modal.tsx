'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { announceVehicleArrival, type AnnounceVehicleArrivalInput } from '@/ai/flows/announce-vehicle-arrivals';
import type { Plant } from '@/types';
import { Mic } from 'lucide-react';
import { IconButton } from '../shared/icon-button';


interface AnnounceVehicleArrivalModalProps {
  plants: Plant[];
}

export function AnnounceVehicleArrivalModal({ plants }: AnnounceVehicleArrivalModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [mixingPlant, setMixingPlant] = useState<string>(plants[0]?.id || '');
  const [vehicleNumber, setVehicleNumber] = useState('');
  const [repeatCount, setRepeatCount] = useState<number>(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async () => {
    if (!mixingPlant || !vehicleNumber || repeatCount < 1) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const selectedPlantName = plants.find(p => p.id === mixingPlant)?.name || mixingPlant;
      const input: AnnounceVehicleArrivalInput = {
        mixingPlant: selectedPlantName,
        vehicleNumber,
        repeatCount,
        // Optional: speechRate, language, accent could be sourced from system settings
      };
      const result = await announceVehicleArrival(input);
      toast({
        title: 'Announcement Queued',
        description: `Announcement: ${result.announcement}`,
      });
      setIsOpen(false);
      // Reset form if needed
      setVehicleNumber('');
      setRepeatCount(1);
    } catch (error) {
      console.error('Failed to announce vehicle arrival:', error);
      toast({
        title: 'Error',
        description: 'Failed to make announcement. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
         <IconButton icon={Mic} tooltipLabel="朗读车辆进站信息" />
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>朗读车辆进站信息</DialogTitle>
          <DialogDescription>
            设置并朗读车辆到达搅拌站的信息。
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="mixingPlant" className="text-right">
              搅拌站
            </Label>
            <Select value={mixingPlant} onValueChange={setMixingPlant}>
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="选择搅拌站" />
              </SelectTrigger>
              <SelectContent>
                {plants.map((plant) => (
                  <SelectItem key={plant.id} value={plant.id}>
                    {plant.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="vehicleNumber" className="text-right">
              车号
            </Label>
            <Input
              id="vehicleNumber"
              value={vehicleNumber}
              onChange={(e) => setVehicleNumber(e.target.value)}
              className="col-span-3"
              placeholder="例如：粤A88888"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="repeatCount" className="text-right">
              朗读次数
            </Label>
            <Input
              id="repeatCount"
              type="number"
              value={repeatCount}
              onChange={(e) => setRepeatCount(parseInt(e.target.value, 10) || 1)}
              className="col-span-3"
              min="1"
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>取消</Button>
          <Button type="submit" onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? '提交中...' : '确认朗读'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
