# TMH 任务调度系统文档管理脚本
# 用于管理和维护项目文档

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("list", "search", "validate", "generate-toc", "backup", "restore")]
    [string]$Action = "list",
    
    [Parameter(Mandatory=$false)]
    [string]$SearchTerm = "",
    
    [Parameter(Mandatory=$false)]
    [string]$BackupPath = "./docs-backup"
)

# 文档目录结构
$DocsStructure = @{
    "features" = @{
        "description" = "功能特性文档"
        "files" = @(
            "CONFIGURABLE_TASK_CARD_IMPLEMENTATION.md",
            "DISPATCH_REMINDER_ENHANCEMENT_SUMMARY.md",
            "DISPATCH_REMINDER_PRIORITY_UPGRADE.md",
            "ENHANCED_CARDS_QUICK_START.md",
            "ENHANCED_CARD_SYSTEM_COMPLETE.md",
            "ENHANCED_CARD_SYSTEM_GUIDE.md",
            "LIST_MODE_DISPATCH_REMINDER_ALIGNMENT.md",
            "TASK_LIST_HEADER_REMOVAL_SUMMARY.md",
            "TASK_LIST_REDESIGN_SUMMARY.md"
        )
    }
    "performance" = @{
        "description" = "性能优化文档"
        "files" = @(
            "CARD_PERFORMANCE_FIX_COMPLETE.md",
            "CARD_PERFORMANCE_OPTIMIZATION_COMPLETE.md",
            "CARD_PERFORMANCE_QUICK_GUIDE.md",
            "CARD_SCROLL_PERFORMANCE_FIX.md"
        )
    }
    "guides" = @{
        "description" = "使用指南文档"
        "files" = @(
            "QUICK_START_GUIDE.md",
            "TASK_LIST_STYLE_CONFIG_GUIDE.md"
        )
    }
    "implementation" = @{
        "description" = "技术实现文档"
        "files" = @(
            "INTEGRATION_COMPLETE_SUMMARY.md",
            "INTEGRATION_EXAMPLE.md",
            "REACT_BEAUTIFUL_DND_IMPLEMENTATION.md"
        )
    }
    "fixes" = @{
        "description" = "问题修复文档"
        "files" = @(
            "STYLE_CONFIG_FINAL_FIX.md",
            "STYLE_CONFIG_FIX_SUMMARY.md",
            "USEMEMO_FIX_COMPLETE.md"
        )
    }
}

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Text,
        [string]$Color = "White"
    )
    
    $colorMap = @{
        "Red" = "Red"
        "Green" = "Green"
        "Yellow" = "Yellow"
        "Blue" = "Blue"
        "Magenta" = "Magenta"
        "Cyan" = "Cyan"
        "White" = "White"
    }
    
    Write-Host $Text -ForegroundColor $colorMap[$Color]
}

# 列出所有文档
function Show-DocumentList {
    Write-ColorOutput "📚 TMH 任务调度系统文档结构" "Cyan"
    Write-ColorOutput "=" * 50 "Gray"
    
    foreach ($category in $DocsStructure.Keys) {
        $info = $DocsStructure[$category]
        Write-ColorOutput "`n📁 $category/ - $($info.description)" "Yellow"
        
        foreach ($file in $info.files) {
            $filePath = "docs/$category/$file"
            if (Test-Path $filePath) {
                Write-ColorOutput "  ✅ $file" "Green"
            } else {
                Write-ColorOutput "  ❌ $file (缺失)" "Red"
            }
        }
    }
    
    # 显示根目录文档
    Write-ColorOutput "`n📁 docs/ (根目录)" "Yellow"
    $rootFiles = @("index.md", "blueprint.md", "项目进化步骤.md")
    foreach ($file in $rootFiles) {
        $filePath = "docs/$file"
        if (Test-Path $filePath) {
            Write-ColorOutput "  ✅ $file" "Green"
        } else {
            Write-ColorOutput "  ❌ $file (缺失)" "Red"
        }
    }
    
    # 统计信息
    $totalFiles = ($DocsStructure.Values | ForEach-Object { $_.files.Count } | Measure-Object -Sum).Sum + 3
    $existingFiles = 0
    
    foreach ($category in $DocsStructure.Keys) {
        foreach ($file in $DocsStructure[$category].files) {
            if (Test-Path "docs/$category/$file") { $existingFiles++ }
        }
    }
    
    foreach ($file in $rootFiles) {
        if (Test-Path "docs/$file") { $existingFiles++ }
    }
    
    Write-ColorOutput "`n📊 文档统计:" "Cyan"
    Write-ColorOutput "  总文档数: $totalFiles" "White"
    Write-ColorOutput "  已存在: $existingFiles" "Green"
    Write-ColorOutput "  缺失: $($totalFiles - $existingFiles)" "Red"
    Write-ColorOutput "  完整度: $([math]::Round($existingFiles / $totalFiles * 100, 1))%" "Yellow"
}

# 搜索文档内容
function Search-Documents {
    param([string]$SearchTerm)
    
    if ([string]::IsNullOrEmpty($SearchTerm)) {
        Write-ColorOutput "❌ 请提供搜索关键词" "Red"
        return
    }
    
    Write-ColorOutput "🔍 搜索关键词: '$SearchTerm'" "Cyan"
    Write-ColorOutput "=" * 50 "Gray"
    
    $results = @()
    
    # 搜索所有 markdown 文件
    Get-ChildItem -Path "docs" -Recurse -Filter "*.md" | ForEach-Object {
        $content = Get-Content $_.FullName -Raw -ErrorAction SilentlyContinue
        if ($content -and $content -match $SearchTerm) {
            $relativePath = $_.FullName.Replace((Get-Location).Path + "\", "")
            $results += @{
                "File" = $relativePath
                "Matches" = ($content | Select-String $SearchTerm -AllMatches).Matches.Count
            }
        }
    }
    
    if ($results.Count -eq 0) {
        Write-ColorOutput "❌ 未找到包含 '$SearchTerm' 的文档" "Red"
    } else {
        Write-ColorOutput "✅ 找到 $($results.Count) 个匹配的文档:" "Green"
        $results | Sort-Object -Property Matches -Descending | ForEach-Object {
            Write-ColorOutput "  📄 $($_.File) ($($_.Matches) 处匹配)" "White"
        }
    }
}

# 验证文档完整性
function Test-DocumentIntegrity {
    Write-ColorOutput "🔍 验证文档完整性..." "Cyan"
    Write-ColorOutput "=" * 50 "Gray"
    
    $issues = @()
    
    # 检查目录结构
    foreach ($category in $DocsStructure.Keys) {
        $categoryPath = "docs/$category"
        if (-not (Test-Path $categoryPath)) {
            $issues += "❌ 缺失目录: $categoryPath"
        }
        
        # 检查 README.md
        $readmePath = "$categoryPath/README.md"
        if (-not (Test-Path $readmePath)) {
            $issues += "❌ 缺失索引文件: $readmePath"
        }
        
        # 检查文档文件
        foreach ($file in $DocsStructure[$category].files) {
            $filePath = "$categoryPath/$file"
            if (-not (Test-Path $filePath)) {
                $issues += "❌ 缺失文档: $filePath"
            } else {
                # 检查文件是否为空
                $content = Get-Content $filePath -Raw -ErrorAction SilentlyContinue
                if ([string]::IsNullOrWhiteSpace($content)) {
                    $issues += "⚠️  空文档: $filePath"
                }
            }
        }
    }
    
    # 检查根目录文档
    if (-not (Test-Path "docs/index.md")) {
        $issues += "❌ 缺失主索引文件: docs/index.md"
    }
    
    # 显示结果
    if ($issues.Count -eq 0) {
        Write-ColorOutput "✅ 文档完整性验证通过！" "Green"
    } else {
        Write-ColorOutput "⚠️  发现 $($issues.Count) 个问题:" "Yellow"
        foreach ($issue in $issues) {
            Write-ColorOutput "  $issue" "Red"
        }
    }
}

# 生成目录
function New-TableOfContents {
    Write-ColorOutput "📋 生成文档目录..." "Cyan"
    
    $toc = @"
# 📚 TMH 任务调度系统文档目录

> 自动生成于 $(Get-Date -Format "yyyy年MM月dd日 HH:mm:ss")

## 📂 文档分类

"@

    foreach ($category in $DocsStructure.Keys | Sort-Object) {
        $info = $DocsStructure[$category]
        $toc += "`n### 📁 $category - $($info.description)`n"
        
        foreach ($file in $info.files | Sort-Object) {
            $fileName = [System.IO.Path]::GetFileNameWithoutExtension($file)
            $toc += "- [$fileName](./$category/$file)`n"
        }
    }
    
    $toc += @"

## 📊 文档统计

| 分类 | 文档数量 | 描述 |
|------|----------|------|
"@

    foreach ($category in $DocsStructure.Keys | Sort-Object) {
        $info = $DocsStructure[$category]
        $count = $info.files.Count
        $toc += "| **$category** | $count | $($info.description) |`n"
    }
    
    $toc += @"

---
**📅 最后更新**: $(Get-Date -Format "yyyy年MM月dd日")  
**🔧 生成工具**: docs/manage-docs.ps1
"@

    $tocPath = "docs/TOC.md"
    $toc | Out-File -FilePath $tocPath -Encoding UTF8
    Write-ColorOutput "✅ 目录已生成: $tocPath" "Green"
}

# 备份文档
function Backup-Documents {
    param([string]$BackupPath)
    
    Write-ColorOutput "💾 备份文档到: $BackupPath" "Cyan"
    
    if (Test-Path $BackupPath) {
        Remove-Item $BackupPath -Recurse -Force
    }
    
    Copy-Item "docs" $BackupPath -Recurse
    
    # 创建备份信息文件
    $backupInfo = @{
        "BackupDate" = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        "TotalFiles" = (Get-ChildItem $BackupPath -Recurse -File).Count
        "BackupSize" = [math]::Round((Get-ChildItem $BackupPath -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB, 2)
    }
    
    $backupInfo | ConvertTo-Json | Out-File "$BackupPath/backup-info.json"
    
    Write-ColorOutput "✅ 备份完成!" "Green"
    Write-ColorOutput "  📁 备份路径: $BackupPath" "White"
    Write-ColorOutput "  📄 文件数量: $($backupInfo.TotalFiles)" "White"
    Write-ColorOutput "  💾 备份大小: $($backupInfo.BackupSize) MB" "White"
}

# 主函数
function Main {
    Write-ColorOutput "🚀 TMH 文档管理工具" "Magenta"
    Write-ColorOutput "=" * 50 "Gray"
    
    switch ($Action) {
        "list" { Show-DocumentList }
        "search" { Search-Documents -SearchTerm $SearchTerm }
        "validate" { Test-DocumentIntegrity }
        "generate-toc" { New-TableOfContents }
        "backup" { Backup-Documents -BackupPath $BackupPath }
        default { 
            Write-ColorOutput "❌ 未知操作: $Action" "Red"
            Write-ColorOutput "可用操作: list, search, validate, generate-toc, backup" "Yellow"
        }
    }
}

# 执行主函数
Main
