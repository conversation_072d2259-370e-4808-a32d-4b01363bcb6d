import type { SVGProps } from 'react';

interface RingProgressProps extends SVGProps<SVGSVGElement> {
  value: number; // 0 to 100
  radius?: number;
  strokeWidth?: number;
  label?: string;
}

export function RingProgress({
  value,
  radius = 8,
  strokeWidth = 2,
  label,
  className,
  ...props
}: RingProgressProps) {
  const normalizedRadius = radius - strokeWidth / 2;
  const circumference = normalizedRadius * 2 * Math.PI;
  const strokeDashoffset = circumference - (value / 100) * circumference;

  return (
    <div className="relative flex items-center justify-center" style={{ width: radius * 2, height: radius * 2 }}>
      <svg
        height={radius * 2}
        width={radius * 2}
        viewBox={`0 0 ${radius * 2} ${radius * 2}`}
        className={className}
        {...props}
      >
        <circle
          className="text-muted-foreground/30"
          strokeWidth={strokeWidth}
          stroke="hsl(var(--block-title-1))"
          fill="transparent"
          r={normalizedRadius}
          cx={radius}
          cy={radius}
        />
        <circle
          strokeWidth={strokeWidth}
          strokeDasharray={circumference + ' ' + circumference}
          style={{ strokeDashoffset }}
          strokeLinecap="round"
          stroke="hsl(var(--accent))"
          fill="transparent"
          r={normalizedRadius}
          cx={radius}
          cy={radius}
          transform={`rotate(-90 ${radius} ${radius})`} // Start from top
        />
      </svg>
      {label && (
        <span className="absolute text-xs font-regular text-foreground" style={{ fontSize: radius / 2.5 > 6 ? `${radius / 2.5}px` : '6px' }}>
          {label}
        </span>
      )}
    </div>
  );
}

    