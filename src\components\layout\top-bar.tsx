'use client';

import React, { useState, useCallback } from 'react';
import { MixingPlantTabs } from '@/components/sections/mixing-plant-tabs';
import { ActionBar } from '@/components/sections/action-bar';
import type { Plant } from '@/types';
import { ThemeSelector } from './theme-selector'; 
import { ReminderMessageListClientWrapper } from './reminder-message-list-client-wrapper';
import { useUiStore } from '@/store/uiStore';
import { Button } from '@/components/ui/button';
import { Bell } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const MemoizedMixingPlantTabs = React.memo(MixingPlantTabs);
const MemoizedActionBar = React.memo(ActionBar);

interface TopBarProps {
  plants: Plant[]; // Prop type remains the same
  onInitiateCrossPlantDispatch: (vehicleId: string, targetPlantId: string) => void;
}

export function TopBar({ 
  plants, 
  onInitiateCrossPlantDispatch,
}: TopBarProps) {
  const { selectedPlantId, setSelectedPlantId } = useUiStore();
  const { toast } = useToast();
  
  return (
    <div className="p-1 border-b bg-card flex flex-col h-full overflow-hidden">
      <div className="flex items-center justify-between flex-shrink-0 mb-0.5">
        <MemoizedMixingPlantTabs 
          plants={plants} 
          selectedPlantId={selectedPlantId} 
          onSelectPlant={setSelectedPlantId} 
          onInitiateCrossPlantDispatch={onInitiateCrossPlantDispatch}
        />
        <div className="flex items-center space-x-1">
          <ReminderMessageListClientWrapper />
          <ThemeSelector />
          
          {/* 添加测试按钮 */}
          {process.env.NODE_ENV === 'development' && (
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => {
                toast({
                  title: '测试提醒',
                  description: '已添加测试提醒消息',
                });
              }}
              className="mx-1"
            >
              <Bell className="h-[1.2rem] w-[1.2rem] text-accent" />
            </Button>
          )}
        </div>
      </div>
      <div className="flex-shrink-0">
        {/* Pass plants to ActionBar if AnnounceVehicleArrivalModal needs it */}
        <MemoizedActionBar plants={plants} />
      </div>
    </div>
  );
}
