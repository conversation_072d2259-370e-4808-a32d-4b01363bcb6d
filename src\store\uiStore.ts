// src/store/uiStore.ts
import { createWithEqualityFn } from 'zustand/traditional';
import type { VehicleDisplayMode, TaskListDisplayMode } from '@/types';
import { taskStatusOptions as defaultTaskStatusOptions } from '@/data/mock-data';

const ALERT_SOUND_THROTTLE_MS = 30000; // 30 seconds

interface UiState {
  selectedPlantId: string | null;
  setSelectedPlantId: (plantId: string | null) => void;

  taskStatusFilter: string;
  setTaskStatusFilter: (status: string) => void;

  vehicleDisplayMode: VehicleDisplayMode;
  setVehicleDisplayMode: (mode: VehicleDisplayMode) => void;

  // 添加车辆列表显示模式
  vehicleListDisplayMode: TaskListDisplayMode;
  setVehicleListDisplayMode: (mode: TaskListDisplayMode) => void;

  shouldPlayAlertSound: number; // Counter
  lastAlertSoundTime: number;
  incrementAlertSoundCounter: () => void;
  // setLastAlertSoundTime: (time: number) => void; // Not directly used externally, incrementAlertSoundCounter handles it
  resetAlertSoundThrottle: () => void; // For testing or specific reset scenarios
}

export const useUiStore = createWithEqualityFn<UiState>((set, get) => ({
  selectedPlantId: null, // Will be initialized by dataStore (appStore) after plants are fetched
  setSelectedPlantId: (plantId) => set({ selectedPlantId: plantId }),

  taskStatusFilter: defaultTaskStatusOptions.find(opt => opt.label === '正在进行')?.value || 'InProgress',
  setTaskStatusFilter: (status) => set({ taskStatusFilter: status }),

  vehicleDisplayMode: 'licensePlate',
  setVehicleDisplayMode: (mode) => set({ vehicleDisplayMode: mode }),

  // 初始化车辆列表显示模式为表格
  vehicleListDisplayMode: 'table',
  setVehicleListDisplayMode: (mode) => set({ vehicleListDisplayMode: mode }),

  shouldPlayAlertSound: 0,
  lastAlertSoundTime: 0,
  incrementAlertSoundCounter: () => {
    if (Date.now() - get().lastAlertSoundTime > ALERT_SOUND_THROTTLE_MS) {
      set(state => ({
        shouldPlayAlertSound: state.shouldPlayAlertSound + 1,
        lastAlertSoundTime: Date.now(),
      }));
    }
  },
  // setLastAlertSoundTime: (time) => set({ lastAlertSoundTime: time }), // Kept if specific direct setting is needed
  resetAlertSoundThrottle: () => set({ lastAlertSoundTime: 0 }),
}), Object.is);

// This function will be called from appStore (data store) after initial plant data is fetched
export const initializeUiStoreWithPlantData = (initialPlantId: string | null, initialTaskStatusFilter?: string) => {
  useUiStore.setState({ 
    selectedPlantId: initialPlantId,
    taskStatusFilter: initialTaskStatusFilter || defaultTaskStatusOptions.find(opt => opt.label === '正在进行')?.value || 'InProgress'
  });
};
