import { createWithEqualityFn } from 'zustand/traditional';
import { persist } from 'zustand/middleware';
import { ReminderMessage, ReminderType, ReminderConfig } from '@/types';
import * as reminderDb from './reminderDb';
import { produce } from 'immer';
import { immerZustand } from '@/utils/immer-helpers';
// 用于生成唯一ID的函数
const generateId = () => Math.random().toString(36).substring(2, 9);

// Check if code is running in browser environment
const isBrowser = typeof window !== 'undefined';

// 默认的提醒级别配置
export const DEFAULT_REMINDER_LEVELS = [
  { minutes: 30, types: ['dispatchCountdown'] as ReminderType[] },
  { minutes: 15, types: ['dispatchCountdown', 'highlight'] as ReminderType[] },
  { minutes: 5, types: ['dispatchCountdown', 'highlight', 'popup', 'sound'] as ReminderType[] }
];

// ReminderState interface
interface ReminderState {
  messages: ReminderMessage[];
  configs: ReminderConfig[];
  addMessage: (message: Omit<ReminderMessage, 'id' | 'time' | 'read'>) => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  clearMessages: () => Promise<void>;
  addErrorMessage: (error: Error, context: string) => Promise<void>;
  addConfig: (config: Omit<ReminderConfig, 'lastUpdated'>) => Promise<void>;
  updateConfig: (taskId: string, config: Partial<ReminderConfig>) => Promise<void>;
  removeConfig: (taskId: string) => Promise<void>;
  importConfigs: (configs: ReminderConfig[]) => Promise<void>;
  exportConfigs: () => ReminderConfig[];
  addMockReminderMessages: () => void;
  generateMockConfigs: (count: number) => void;
}

export const useReminderStore = createWithEqualityFn<ReminderState>((set, get) => ({
      messages: [],
      configs: [],
  // addMessage: async (message) => {
  //   const newMsg: ReminderMessage = { ...message, id: generateId(), time: Date.now(), read: false };
  //   set((state) => ({ messages: [newMsg, ...state.messages] }));
  //   await reminderDb.addMessage(newMsg);
  // },
  addMessage: async (message) => {
  const newMsg: ReminderMessage = { ...message, id: generateId(), time: Date.now(), read: false };
  set(produce((draft) => {
    draft.messages.unshift(newMsg);
  }));
  await reminderDb.addMessage(newMsg);
},
  // markAsRead: async (id) => {
  //   set((state) => ({ messages: state.messages.map(m => m.id === id ? { ...m, read: true } : m) }));
  //   await reminderDb.updateMessage(id, { read: true });
  // },
  markAsRead: async (id) => {
  set(produce((draft) => {
    const message = draft.messages.find((m: { id: string; }) => m.id === id);
    if (message) {
      message.read = true;
    }
  }));
  await reminderDb.updateMessage(id, { read: true });
},
  markAllAsRead: async () => {
  set(produce((draft) => {
    draft.messages.forEach((message: { read: boolean; }) => {
      message.read = true;
    });
  }));
},
  clearMessages: async () => {
    set({ messages: [] });
    await reminderDb.clearAll();
  },
  addErrorMessage: async (error, context) => {
    const newMsg: ReminderMessage = {
          id: generateId(),
      time: Date.now(),
      read: false,
      type: 'error',
      taskId: '',
      taskNumber: '',
          title: `${context}错误`,
          description: error.message,
      projectName: '',
    };
    set((state) => ({ messages: [newMsg, ...state.messages] }));
    await reminderDb.addMessage(newMsg);
  },
  addConfig: async (config) => {
    const newConfig: ReminderConfig = { ...config, lastUpdated: Date.now() };
    set((state) => ({ configs: [...state.configs.filter(c => c.taskId !== config.taskId), newConfig] }));
    await reminderDb.addOrUpdateConfig(newConfig);
  },
  // 
  updateConfig: async (taskId, patch) => {
  set(produce((draft) => {
    const config = draft.configs.find((c: { taskId: string; }) => c.taskId === taskId);
    if (config) {
      Object.assign(config, patch, { lastUpdated: Date.now() });
    }
  }));
},
  removeConfig: async (taskId) => {
    set((state) => ({ configs: state.configs.filter(c => c.taskId !== taskId) }));
    await reminderDb.removeConfig(taskId);
  },
  importConfigs: async (configs) => {
    set({ configs });
    for (const c of configs) await reminderDb.addOrUpdateConfig(c);
  },
  exportConfigs: () => get().configs,
  addMockReminderMessages: () => {},
  generateMockConfigs: (count) => {},
}), Object.is);

// 初始化加载辅助函数，模块顶层调用一次
export async function reminderStoreInit() {
  // Skip initialization in server-side environment
  if (!isBrowser) return;
  
  try {
    const [configs, messages] = await Promise.all([
      reminderDb.getAllConfigs(),
      reminderDb.getAllMessages(),
    ]);
    useReminderStore.setState({ configs, messages });
  } catch (error) {
    console.error('Failed to initialize reminder store:', error);
    // Safely continue with empty state
  }
}

// 添加临时测试数据的辅助函数
export const addMockReminderMessages = () => {
  const mockMessages = [
    {
      taskId: 'task_stress_p1_1',
      taskNumber: 'S10001',
      type: 'dispatchCountdown' as ReminderType,
      title: '发车提醒',
      description: '任务 S10001（压力测试项目1）距离下次发车时间还有5分钟',
      projectName: '压力测试项目1',
    },
    {
      taskId: 'task_stress_p1_2',
      taskNumber: 'S10002',
      type: 'highlight' as ReminderType,
      title: '发车提醒',
      description: '任务 S10002（压力测试项目2）已到发车时间',
      projectName: '压力测试项目2',
    },
    {
      taskId: 'task_stress_p1_3',
      taskNumber: 'S10003',
      type: 'popup' as ReminderType,
      title: '发车提醒',
      description: '任务 S10003（压力测试项目3）需要立即发车',
      projectName: '压力测试项目3',
    },
    {
      taskId: 'task_stress_p1_4',
      taskNumber: 'S10004',
      type: 'sound' as ReminderType,
      title: '声音提醒',
      description: '任务 S10004（压力测试项目4）即将发车，请注意',
      projectName: '压力测试项目4',
    },
    {
      taskId: 'task_stress_p1_5',
      taskNumber: 'S10005',
      type: 'dispatchCountdown' as ReminderType,
      title: '发车提醒',
      description: '任务 S10005（压力测试项目5）距离下次发车时间还有15分钟',
      projectName: '压力测试项目5',
    },
  ];
  
  const store = useReminderStore.getState();
  mockMessages.forEach(msg => {
    store.addMessage(msg);
  });
  
  console.log('已添加5条模拟提醒消息');
};

// 添加生成大量模拟配置的辅助函数
export const generateMockReminderConfigs = (count: number = 500) => {
  const store = useReminderStore.getState();
  store.generateMockConfigs(count);
}; 

// 初始化加载 - only run in browser context
if (isBrowser) {
  reminderStoreInit(); 
}