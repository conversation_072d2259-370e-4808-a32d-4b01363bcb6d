// src/hooks/useFilteredTasks.ts
import { useMemo } from 'react';
import { useAppStore } from '@/store/appStore';
import { useUiStore } from '@/store/uiStore';
import { filterTasksForDisplay } from '@/services/taskFilteringService';
import type { Task } from '@/types';
import { shallow } from 'zustand/shallow';

export function useFilteredTasks(): Task[] {
  const tasks = useAppStore(state => state.tasks, shallow);
  const selectedPlantId = useUiStore(state => state.selectedPlantId);
  const taskStatusFilter = useUiStore(state => state.taskStatusFilter);

  // useMemo will re-calculate only if tasks, selectedPlantId, or taskStatusFilter change.
  return useMemo(() => {
    return filterTasksForDisplay(tasks, selectedPlantId, taskStatusFilter);
  }, [tasks, selectedPlantId, taskStatusFilter]);
}
