// API配置管理
export interface ApiConfig {
  baseUrl: string;
  timeout: number;
  retries: number;
  endpoints: {
    tasks: string;
    vehicles: string;
    deliveryOrders: string;
    plants: string;
  };
}

// 环境配置
const configs: Record<string, ApiConfig> = {
  development: {
    baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api',
    timeout: 10000,
    retries: 3,
    endpoints: {
      tasks: '/tasks',
      vehicles: '/vehicles', 
      deliveryOrders: '/delivery-orders',
      plants: '/plants',
    },
  },
  production: {
    baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.yourdomain.com',
    timeout: 15000,
    retries: 2,
    endpoints: {
      tasks: '/tasks',
      vehicles: '/vehicles',
      deliveryOrders: '/delivery-orders', 
      plants: '/plants',
    },
  },
  mock: {
    baseUrl: '',
    timeout: 100,
    retries: 0,
    endpoints: {
      tasks: '/tasks',
      vehicles: '/vehicles',
      deliveryOrders: '/delivery-orders',
      plants: '/plants',  // 改为 '/mock/plants'
    },
  },
};

// 获取当前环境配置
export function getApiConfig(): ApiConfig {
  const env = process.env.NODE_ENV || 'development';

  // 检查是否使用mock数据
  let useMock = process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true';

  // 在客户端，也检查localStorage和window对象
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('tmh_data_source');
    if (stored) {
      useMock = stored === 'mock';
    } else if ((window as any).__NEXT_PUBLIC_USE_MOCK_DATA) {
      useMock = (window as any).__NEXT_PUBLIC_USE_MOCK_DATA === 'true';
    }
  }

  if (useMock) {
    return configs.mock;
  }

  return configs[env] || configs.development;
}

// 构建完整URL
export function buildUrl(endpoint: string, params?: Record<string, string>): string {
  const config = getApiConfig();
  
  // 如果是mock模式，返回空字符串（将被拦截）
  if (config.baseUrl === '') {
    return '';
  }
  
  let url = `${config.baseUrl}${endpoint}`;
  
  if (params) {
    const searchParams = new URLSearchParams(params);
    url += `?${searchParams.toString()}`;
  }
  
  return url;
}
