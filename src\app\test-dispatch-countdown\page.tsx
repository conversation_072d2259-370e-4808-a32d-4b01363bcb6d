'use client';

import { useState } from 'react';
import { DispatchCountdown } from '@/components/ui/dispatch-countdown';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestDispatchCountdownPage() {
  const [refreshKey, setRefreshKey] = useState(0);

  // 模拟不同状态的数据
  const testCases = [
    {
      title: '待定状态',
      description: '没有设置发车频率和时间',
      props: {
        nextScheduledTime: null,
        lastDispatchTime: null,
        dispatchFrequencyMinutes: 0,
        dispatchStatus: 'New' as const,
      }
    },
    {
      title: '等待中 - 还有2小时',
      description: '距离下次发车还有较长时间',
      props: {
        nextScheduledTime: new Date(Date.now() + 2 * 60 * 60 * 1000),
        lastDispatchTime: new Date(Date.now() - 30 * 60 * 1000),
        dispatchFrequencyMinutes: 150,
        dispatchStatus: 'ReadyToProduce' as const,
      }
    },
    {
      title: '即将发车 - 还有3分钟',
      description: '距离下次发车时间较短',
      props: {
        nextScheduledTime: new Date(Date.now() + 3 * 60 * 1000),
        lastDispatchTime: new Date(Date.now() - 27 * 60 * 1000),
        dispatchFrequencyMinutes: 30,
        dispatchStatus: 'RatioSet' as const,
      }
    },
    {
      title: '紧急 - 还有30秒',
      description: '即将到达发车时间',
      props: {
        nextScheduledTime: new Date(Date.now() + 30 * 1000),
        lastDispatchTime: new Date(Date.now() - 29.5 * 60 * 1000),
        dispatchFrequencyMinutes: 30,
        dispatchStatus: 'InProgress' as const,
      }
    },
    {
      title: '超时 - 已超时5分钟',
      description: '已经超过计划发车时间',
      props: {
        nextScheduledTime: new Date(Date.now() - 5 * 60 * 1000),
        lastDispatchTime: new Date(Date.now() - 35 * 60 * 1000),
        dispatchFrequencyMinutes: 30,
        dispatchStatus: 'Paused' as const,
      }
    },
    {
      title: '已完成',
      description: '没有发车频率，显示上次发车时间',
      props: {
        nextScheduledTime: null,
        lastDispatchTime: new Date(Date.now() - 15 * 60 * 1000),
        dispatchFrequencyMinutes: 0,
        dispatchStatus: 'Completed' as const,
      }
    }
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">发车提醒倒计时组件测试</h1>
          <p className="text-muted-foreground mt-2">
            展示不同状态下的发车提醒倒计时效果
          </p>
        </div>
        <Button 
          onClick={() => setRefreshKey(prev => prev + 1)}
          variant="outline"
        >
          刷新组件
        </Button>
      </div>

      {/* 紧凑模式展示 */}
      <Card>
        <CardHeader>
          <CardTitle>紧凑模式 (Table模式)</CardTitle>
          <CardDescription>
            适用于表格中的单元格显示
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {testCases.map((testCase, index) => (
              <div key={`compact-${index}-${refreshKey}`} className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  {testCase.title}
                </div>
                <DispatchCountdown
                  {...testCase.props}
                  compact={true}
                  showIcon={true}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 完整模式展示 */}
      <Card>
        <CardHeader>
          <CardTitle>完整模式 (卡片模式)</CardTitle>
          <CardDescription>
            适用于卡片视图或详细信息显示
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {testCases.map((testCase, index) => (
              <div key={`full-${index}-${refreshKey}`} className="space-y-2">
                <div className="text-sm font-medium">
                  {testCase.title}
                </div>
                <div className="text-xs text-muted-foreground mb-2">
                  {testCase.description}
                </div>
                <DispatchCountdown
                  {...testCase.props}
                  compact={false}
                  showIcon={true}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 无图标模式 */}
      <Card>
        <CardHeader>
          <CardTitle>无图标模式</CardTitle>
          <CardDescription>
            适用于空间受限的场景
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {testCases.map((testCase, index) => (
              <div key={`no-icon-${index}-${refreshKey}`} className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  {testCase.title}
                </div>
                <DispatchCountdown
                  {...testCase.props}
                  compact={true}
                  showIcon={false}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 实时更新演示 */}
      <Card>
        <CardHeader>
          <CardTitle>实时更新演示</CardTitle>
          <CardDescription>
            观察倒计时的实时变化效果
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="font-medium">紧凑模式实时倒计时</h3>
              <div className="space-y-2">
                <DispatchCountdown
                  nextScheduledTime={new Date(Date.now() + 65 * 1000)}
                  lastDispatchTime={new Date(Date.now() - 25 * 60 * 1000)}
                  dispatchFrequencyMinutes={30}
                  dispatchStatus="InProgress"
                  compact={true}
                  showIcon={true}
                />
                <div className="text-xs text-muted-foreground">
                  1分钟后发车 (每秒更新)
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <h3 className="font-medium">完整模式实时倒计时</h3>
              <DispatchCountdown
                nextScheduledTime={new Date(Date.now() + 125 * 1000)}
                lastDispatchTime={new Date(Date.now() - 28 * 60 * 1000)}
                dispatchFrequencyMinutes={30}
                dispatchStatus="RatioSet"
                compact={false}
                showIcon={true}
              />
              <div className="text-xs text-muted-foreground">
                2分钟后发车 (每秒更新，带进度条)
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 设计说明 */}
      <Card>
        <CardHeader>
          <CardTitle>设计特点</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h3 className="font-medium">视觉设计</h3>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• 状态色彩区分：蓝色(等待) → 黄色(警告) → 橙色(紧急) → 红色(超时)</li>
                <li>• 渐变进度条显示时间进度</li>
                <li>• 圆角边框和柔和阴影</li>
                <li>• 响应式图标和文字大小</li>
                <li>• 深色模式适配</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h3 className="font-medium">交互体验</h3>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• 悬停显示详细信息工具提示</li>
                <li>• 实时更新，无需手动刷新</li>
                <li>• 紧凑和完整两种显示模式</li>
                <li>• 智能时间格式化</li>
                <li>• 平滑的动画过渡效果</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
