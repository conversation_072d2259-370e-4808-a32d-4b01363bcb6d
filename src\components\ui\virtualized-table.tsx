// src/components/ui/virtualized-table.tsx
"use client";

import React, { useRef, useCallback } from "react";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  Row,
  Header,
  ColumnSizingState,
  VisibilityState,
  ColumnOrderState,
  OnChangeFn,
  Table as ReactTableType,
} from "@tanstack/react-table";
import { useVirtualizer } from "@tanstack/react-virtual";
import { cn } from "@/lib/utils";
import type { CustomColumnDefinition, DensityStyleValues, Task, Vehicle } from "@/types";
import { useTaskSelectionActions, useTaskSelectionState } from "@/contexts/TaskSelectionContext";
import { ItemTypes } from '@/constants/dndItemTypes';
import { ZIndexLevels } from '@/types/sticky-columns';
import { DraggableHeader } from './draggable-header';
import { ColumnDragDropProvider } from './column-drag-drop-provider';
import { useColumnDragDropContext } from '@/contexts/ColumnDragDropContext';

export interface VirtualizedTableProps<TData extends object> {
  data: TData[];
  columns: ColumnDef<TData, any>[];
  getRowId?: (originalRow: TData, index: number, parent?: Row<TData>) => string;
  densityStyles: DensityStyleValues;
  enableZebraStriping: boolean;
  estimateRowHeight: (task?: TData) => number;
  totalTableWidth: number;

  columnSizing: ColumnSizingState;
  onColumnSizingChange?: OnChangeFn<ColumnSizingState>;
  columnVisibility: VisibilityState;
  onColumnVisibilityChange?: OnChangeFn<VisibilityState>;
  columnOrder: ColumnOrderState;
  onColumnOrderChange?: OnChangeFn<ColumnOrderState>;

  onHeaderContextMenu?: (event: React.MouseEvent, column: CustomColumnDefinition) => void;
  onHeaderDoubleClick?: (event: React.MouseEvent, column: CustomColumnDefinition) => void;
  onRowContextMenu?: (event: React.MouseEvent, row: Row<TData>) => void;
  onRowDoubleClick?: (row: Row<TData>) => void;

  onDropOnProductionLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  getColumnBackgroundProps?: (columnId: string, isHeader: boolean, isFixed: boolean) => { style: React.CSSProperties, className: string };
  isGroupedMode?: boolean; // 新增：是否为分组模式
}

// DraggableHeader component and related functionality has been moved to separate files
// See ./draggable-header.tsx and ./column-drag-drop-provider.tsx
// This improves code organization and maintainability

const TableRowWrapper = React.memo(({ row, children, estimateRowHeight, getRowClassName, index }: {
  row: Row<any>;
  children: React.ReactNode;
  estimateRowHeight: () => number;
  getRowClassName: (row: Row<any>, index: number) => string;
  index: number;
}) => {
  const task = row.original as Task;
  const { setSelectedTask } = useTaskSelectionActions();
  const { isTaskSelected } = useTaskSelectionState();

  // 先计算选中状态
  const isSelected = task ? isTaskSelected(task.id) : false;

  const handleRowClick = useCallback((e: React.MouseEvent) => {
    // 避免在点击按钮或其他交互元素时触发行选中
    const target = e.target as HTMLElement;
    if (target.closest('button, a, input, select, textarea, [role="button"]')) {
      return;
    }

    // 切换选中状态：如果已选中则取消选中，否则选中
    if (isSelected) {
      setSelectedTask(null);
    } else {
      setSelectedTask(task);
    }
  }, [task, setSelectedTask, isSelected]);

  return (
    <tr
      key={row.id}
      data-index={index}
      data-task-id={task?.id}
      data-row-id={task?.id}
      onClick={handleRowClick}
      className={cn(
        getRowClassName(row, index),
        'virtual-row-performance', // Ensure this class applies `contain: layout style;`
        'task-row-clickable', // 添加点击样式
        isSelected && 'task-row-selected' // 添加选中样式
      )}
      style={{ height: `${estimateRowHeight()}px` }}
    >
      {children}
    </tr>
  );
});
TableRowWrapper.displayName = 'TableRowWrapper';

export function VirtualizedTable<TData extends object>({
  data,
  columns,
  getRowId,
  densityStyles,
  enableZebraStriping,
  estimateRowHeight,
  totalTableWidth,
  columnSizing,
  onColumnSizingChange,
  columnVisibility,
  onColumnVisibilityChange,
  columnOrder,
  onColumnOrderChange,
  onHeaderContextMenu,
  onHeaderDoubleClick,
  onRowContextMenu,
  onRowDoubleClick,
  onDropOnProductionLine,
  getColumnBackgroundProps,
  isGroupedMode = false,
}: VirtualizedTableProps<TData>) {
  const tableContainerRef = useRef<HTMLDivElement>(null);

  const table = useReactTable({
    data,
    columns,
    getRowId,
    state: {
      columnSizing,
      columnVisibility,
      columnOrder,
    },
    onColumnSizingChange,
    onColumnVisibilityChange,
    onColumnOrderChange,
    getCoreRowModel: getCoreRowModel(),
    columnResizeMode: "onChange",
    meta: { densityStyles, onDropOnProductionLine, getColumnBackgroundProps },
    // Force re-render when getColumnBackgroundProps changes
    autoResetAll: false,
    enableColumnResizing: true,
  });

  const { rows } = table.getRowModel();
  const rowVirtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => tableContainerRef.current,
    estimateSize: (index) => estimateRowHeight(rows[index]?.original),
    overscan: rows.length<50? 50 : rows.length / 10 *2 ,
    scrollMargin: 0,
    initialOffset: 0,
  });

  const virtualRows = rowVirtualizer.getVirtualItems();
  const virtualizerTotalSize = rowVirtualizer.getTotalSize();
  const paddingTop = virtualRows.length > 0 ? virtualRows[0]?.start || 0 : 0;
  const paddingBottom =
    virtualRows.length > 0
      ? virtualizerTotalSize - (virtualRows[virtualRows.length - 1]?.end || 0)
      : 0;

  const headerGroups = table.getHeaderGroups();

  // Column drag-drop functionality has been moved to ColumnDragDropProvider component
  // This allows for better separation of concerns and reusability
  
  /**
   * @function getRowClassName
   * @description Get the CSS class name for a table row based on its properties and index
   */

  const getRowClassName = React.useCallback(
    (row: Row<TData>, rowIdx: number) => {
      const classes = [
        "tr", 
        rowIdx % 2 === 0 ? "" : enableZebraStriping ? "bg-muted/30" : "",
      ];
      if ((row.original as Task)?.isDueForDispatch === true) {
        classes.push("task-row-dispatch-due");
      }
      return classes.join(" ");
    },
    [enableZebraStriping]
  );

  return (
    <ColumnDragDropProvider
      tableInstance={table}
      initialColumnOrder={columnOrder}
      onColumnOrderChange={onColumnOrderChange}
      disableDragDrop={false}
    >
      <div
        ref={tableContainerRef}
        className={cn(
          "custom-scrollbar gpu-accelerated",
          isGroupedMode ? "h-auto overflow-visible min-h-0" : "virtualized-table-container"
        )}
        style={isGroupedMode ? { minHeight: 'fit-content' } : undefined}
      >
        <table
          style={{ width: '100%', minWidth: Math.max(totalTableWidth, 800), tableLayout: 'fixed' }}
          className="virtualized-table"
        >
        <thead
          className="sticky top-0"
          style={{ zIndex: ZIndexLevels.TABLE_THEAD_STICKY }}
        >
          {headerGroups.map((headerGroup)=> (
            <tr key={headerGroup.id} className="border-b">
              {headerGroup.headers.map((header, headerIndex) => (
                <DraggableHeader
                  key={header.id}
                  header={header}
                  getColumnBackgroundProps={getColumnBackgroundProps}
                  onHeaderContextMenu={onHeaderContextMenu}
                  onHeaderDoubleClick={onHeaderDoubleClick}
                  index={headerIndex}
                  tableInstance={table}
                />
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {paddingTop > 0 && (
            <tr style={{ height: `${paddingTop}px` }} />
          )}
          {virtualRows.map((virtualRow) => {
            const row = rows[virtualRow.index] as Row<TData>;
            return (
               <TableRowWrapper
                key={row.id}
                row={row}
                estimateRowHeight={() => estimateRowHeight(row.original)}
                getRowClassName={getRowClassName}
                index={virtualRow.index}
              >
                {row.getVisibleCells().map((cell) => {
                  const customDefMeta = cell.column.columnDef.meta as { customDef?: CustomColumnDefinition & { densityStyles?: DensityStyleValues } } | undefined;
                  const customDef = customDefMeta?.customDef;

                  let finalStyle: React.CSSProperties = {
                    width: cell.column.getSize(),
                    minWidth: cell.column.columnDef.minSize,
                    maxWidth: cell.column.columnDef.maxSize,
                  };
                  let finalClassName = cn(
                    "align-middle whitespace-nowrap relative", // Added relative for pseudo-elements
                    densityStyles.cellPaddingX,
                    densityStyles.cellPaddingY
                  );

                  const visibleLeafColumns = table.getVisibleLeafColumns();
                  let isLastVisibleLeftFixedCell = false;
                  let isFirstVisibleRightFixedCell = false;

                  if (customDef?.fixed) {
                    finalStyle.position = 'sticky';
                    let offset = 0;
                    const leftFixedVisibleColumns = visibleLeafColumns.filter(col => (col.columnDef.meta as any)?.customDef?.fixed === 'left');
                    const rightFixedVisibleColumns = visibleLeafColumns.filter(col => (col.columnDef.meta as any)?.customDef?.fixed === 'right');

                    if (customDef.fixed === 'left') {
                      isLastVisibleLeftFixedCell = leftFixedVisibleColumns.length > 0 && leftFixedVisibleColumns[leftFixedVisibleColumns.length - 1].id === cell.column.id;
                      for (const col of leftFixedVisibleColumns) {
                        if (col.id === cell.column.id) break;
                        offset += col.getSize();
                      }
                      finalStyle.left = `${offset}px`;
                    } else { // 'right'
                      isFirstVisibleRightFixedCell = rightFixedVisibleColumns.length > 0 && rightFixedVisibleColumns[0].id === cell.column.id;
                      const currentIndex = rightFixedVisibleColumns.findIndex(c => c.id === cell.column.id);
                      for (let i = currentIndex + 1; i < rightFixedVisibleColumns.length; i++) {
                        offset += rightFixedVisibleColumns[i].getSize();
                      }
                      finalStyle.right = `${offset}px`;
                    }
                    
                    // Background application for fixed cells
                    const userBgProps = (getColumnBackgroundProps && customDef.isStyleable !== false)
                      ? getColumnBackgroundProps(cell.column.id, false, true)
                      : { style: {}, className: '' };
                    
                    let userHasOpaqueBg = false;
                    if (userBgProps.style.backgroundColor) {
                      finalStyle.backgroundColor = userBgProps.style.backgroundColor;
                      userHasOpaqueBg = true; 
                    }
                    if (userBgProps.className) {
                      const bgClassFromUser = userBgProps.className.split(' ').find(cls => cls.startsWith('bg-') && !cls.includes('/') && !cls.includes('transparent'));
                      if (bgClassFromUser) {
                        finalClassName = cn(finalClassName, bgClassFromUser);
                        userHasOpaqueBg = true;
                      }
                      finalClassName = cn(finalClassName, userBgProps.className.split(' ').filter(cls => !cls.startsWith('bg-') || cls.includes('/')).join(' '));
                    }

                    if (!userHasOpaqueBg) { // Fallback for fixed cells if user didn't provide opaque bg
                      finalStyle.backgroundColor = 'hsl(var(--fixed-column-cell-background-default))';
                    }

                    // Z-Index and Shadow Class application
                    if (isLastVisibleLeftFixedCell) {
                      finalClassName = cn(finalClassName, 'sticky-shadow-caster-right');
                      finalStyle.zIndex = ZIndexLevels.STICKY_BODY;
                    } else if (isFirstVisibleRightFixedCell) {
                      finalClassName = cn(finalClassName, 'sticky-shadow-caster-left');
                      finalStyle.zIndex = ZIndexLevels.STICKY_BODY;
                    } else {
                      finalStyle.zIndex = ZIndexLevels.STICKY_BODY;
                    }
                  } else { // Not fixed
                     const userBgProps = (getColumnBackgroundProps && customDef && customDef.isStyleable !== false)
                      ? getColumnBackgroundProps(cell.column.id, false, false)
                      : { style: {}, className: '' };
                     if (userBgProps.style.backgroundColor) {
                        finalStyle.backgroundColor = userBgProps.style.backgroundColor;
                     }
                     finalClassName = cn(finalClassName, userBgProps.className);
                     finalStyle.zIndex = ZIndexLevels.TABLE_CELL;
                  }

                  return (
                    <td
                      key={cell.id}
                      data-column-id={cell.column.id}
                      style={finalStyle}
                      className={finalClassName}
                      title={(typeof cell.getValue() === 'string' || typeof cell.getValue() === 'number') && !(customDef?.id === 'dispatchedVehicles' || customDef?.id === 'productionLines') ? String(cell.getValue()) : undefined}
                      onContextMenu={onRowContextMenu ? (e) => onRowContextMenu(e, row) : undefined}
                      onDoubleClick={onRowDoubleClick ? () => onRowDoubleClick(row) : undefined}
                    >
                      {/* Inner div for content and truncation, NOT for overflow:hidden on td itself */}
                      <div className={cn("truncate text-ellipsis whitespace-nowrap w-full h-full overflow-hidden",
                        (customDef?.densityStyles?.cellFontSize || densityStyles.cellFontSize),
                        (customDef?.densityStyles?.cellFontWeight || densityStyles.cellFontWeight)
                      )}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </div>
                    </td>
                  );
                })}
              </TableRowWrapper>
            );
          })}
          {paddingBottom > 0 && (
            <tr style={{ height: `${paddingBottom}px` }} />
          )}
        </tbody>
      </table>
    </div>
    </ColumnDragDropProvider>
  );
}
