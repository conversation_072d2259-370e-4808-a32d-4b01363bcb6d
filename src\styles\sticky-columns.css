/**
 * 固定列样式管理文件
 * 统一管理所有固定列相关的样式，包括阴影效果、层级管理等
 */

/* CSS变量定义 - 便于主题切换和维护 */
:root {
  /* z-index层级变量 - Actual z-index values will be applied via JS using ZIndexLevels enum */
}

/*
  Styles for sticky column shadows using pseudo-elements.
  The parent th/td needs:
  - position: sticky;
  - An appropriate z-index (e.g., from ZIndexLevels.STICKY_HEADER/STICKY_BODY)
  - An opaque background
  - NO overflow: hidden (overflow is handled by an inner div for content)
*/

.sticky-shadow-caster-right {
  position: relative; /* Needed for absolute positioning of pseudo-element */
}

.sticky-shadow-caster-right::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0px; /* Adjusted for wider shadow */
  width: 7px;   /* Adjusted for wider shadow */
  height: 100%;
  box-shadow: 6px 0 8px -2px rgba(0, 0, 0, 0.30); /* Optimized shadow */
  z-index: -1;  /* Behind the parent cell's content */
  pointer-events: none; /* Shadow should not interfere with mouse events */
}

.sticky-shadow-caster-left {
  position: relative; /* Needed for absolute positioning of pseudo-element */
}

.sticky-shadow-caster-left::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1px; /* Adjusted for wider shadow */
  width: 7px;  /* Adjusted for wider shadow */
  height: 100%;
  box-shadow: -6px 0 8px -2px rgba(0, 0, 0, 0.30); /* Optimized shadow */
  z-index: -1; /* Behind the parent cell's content */
  pointer-events: none; /* Shadow should not interfere with mouse events */
}


