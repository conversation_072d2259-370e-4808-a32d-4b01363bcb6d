
// src/hooks/useTableColumnInteractions.ts
'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import type { CustomColumnDefinition } from '@/types';
import { debounce, throttle } from 'lodash-es';
interface UseTableColumnInteractionsProps {
  initialColumnOrder: string[];
  initialColumnWidths: Record<string, number>;
  onColumnOrderChange: (newOrder: string[]) => void;
  onColumnWidthsChange: (columnId: string, newWidth: number) => void;
  allColumnDefinitions: CustomColumnDefinition[];
  tableRef: React.RefObject<HTMLTableElement>;
}

export function useTableColumnInteractions({
  initialColumnOrder,
  initialColumnWidths,
  onColumnOrderChange,
  onColumnWidthsChange,
  allColumnDefinitions,
  tableRef,
}: UseTableColumnInteractionsProps) {
  const [currentColumnOrder, setCurrentColumnOrder] = useState<string[]>(initialColumnOrder);
  const [currentColumnWidths, setCurrentColumnWidths] = useState<Record<string, number>>(initialColumnWidths);

  const [resizingColumnId, setResizingColumnId] = useState<string | null>(null);
  const resizeStartXRef = useRef<number | null>(null);
  const initialResizeWidthRef = useRef<number | null>(null);
  const resizableColumnDefRef = useRef<CustomColumnDefinition | null>(null);

  const [draggedHeaderId, setDraggedHeaderId] = useState<string | null>(null);
  const [dragOverHeaderId, setDragOverHeaderId] = useState<string | null>(null);

  useEffect(() => {
    setCurrentColumnOrder(initialColumnOrder);
  }, [initialColumnOrder]);

  useEffect(() => {
    setCurrentColumnWidths(initialColumnWidths);
  }, [initialColumnWidths]);

  const handleResizeMouseDown = useCallback((event: React.MouseEvent<HTMLDivElement>, columnId: string) => {
    event.preventDefault();
    event.stopPropagation();
    setResizingColumnId(columnId);
    resizeStartXRef.current = event.clientX;
    initialResizeWidthRef.current = currentColumnWidths[columnId] || allColumnDefinitions.find(c => c.id === columnId)?.defaultWidth || 100;
    resizableColumnDefRef.current = allColumnDefinitions.find(c => c.id === columnId) || null;
    
    // 优化拖拽时的视觉反馈
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
    document.body.classList.add('column-resizing');
    
    // 添加列调整时的视觉指示
    const columnHeader = document.querySelector(`[data-column-id="${columnId}"]`);
    if (columnHeader) {
      columnHeader.classList.add('column-resizing-active');
    }
  }, [currentColumnWidths, allColumnDefinitions]);

  const handleResizeMouseMove = useCallback(throttle((event: MouseEvent) => {
    if (!resizingColumnId || resizeStartXRef.current === null || initialResizeWidthRef.current === null || !resizableColumnDefRef.current) return;
    event.preventDefault();
    const deltaX = event.clientX - resizeStartXRef.current;
    let newWidth = initialResizeWidthRef.current + deltaX;
    const minWidth = resizableColumnDefRef.current.minWidth || 50;
    newWidth = Math.max(minWidth, newWidth);

    setCurrentColumnWidths(prev => ({ ...prev, [resizingColumnId]: newWidth }));
  },16), [resizingColumnId]);

  const handleResizeMouseUp = useCallback(() => {
    if (resizingColumnId && currentColumnWidths[resizingColumnId]) {
      onColumnWidthsChange(resizingColumnId, currentColumnWidths[resizingColumnId]);
      
      // 添加调整完成的视觉反馈
      const columnHeader = document.querySelector(`[data-column-id="${resizingColumnId}"]`);
      if (columnHeader) {
        columnHeader.classList.remove('column-resizing-active');
        columnHeader.classList.add('column-resize-complete');
        setTimeout(() => {
          columnHeader.classList.remove('column-resize-complete');
        }, 200);
      }
    }
    
    setResizingColumnId(null);
    resizeStartXRef.current = null;
    initialResizeWidthRef.current = null;
    resizableColumnDefRef.current = null;
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
    document.body.classList.remove('column-resizing');
  }, [resizingColumnId, currentColumnWidths, onColumnWidthsChange]);

  useEffect(() => {
    if (resizingColumnId) {
      window.addEventListener('mousemove', handleResizeMouseMove);
      window.addEventListener('mouseup', handleResizeMouseUp);
    }
    return () => {
      window.removeEventListener('mousemove', handleResizeMouseMove);
      window.removeEventListener('mouseup', handleResizeMouseUp);
      if (document.body.style.cursor === 'col-resize') {
        document.body.style.cursor = '';
      }
      if (document.body.style.userSelect === 'none') {
        document.body.style.userSelect = '';
      }
    };
  }, [resizingColumnId, handleResizeMouseMove, handleResizeMouseUp]);

  const handleHeaderDragStart = useCallback((event: React.DragEvent<HTMLTableCellElement>, columnId: string) => {
    console.log('拖拽开始:', columnId);
    const colDef = allColumnDefinitions.find(c => c.id === columnId);
    if (colDef && colDef.isReorderable === false) {
        console.log('列不可重新排序:', columnId);
        event.preventDefault();
        return;
    }
    event.dataTransfer.setData('text/plain', columnId);
    event.dataTransfer.effectAllowed = 'move';
    setDraggedHeaderId(columnId);
    console.log('拖拽状态已设置:', columnId);
    
    // 添加拖拽开始的视觉反馈
    const draggedElement = event.currentTarget;
    draggedElement.classList.add('column-dragging');
    document.body.classList.add('column-reordering');
    
    // 设置拖拽图像的透明度
    setTimeout(() => {
      if (draggedElement) {
        draggedElement.style.opacity = '0.5';
      }
    }, 0);
  }, [allColumnDefinitions]);

  const handleHeaderDragOver = useCallback((event: React.DragEvent<HTMLTableCellElement>) => {
    event.preventDefault();
    if (draggedHeaderId) {
        event.dataTransfer.dropEffect = 'move';
    }
  }, [draggedHeaderId]);

  const handleHeaderDragEnter = useCallback((event: React.DragEvent<HTMLTableCellElement>, columnId: string) => {
    event.preventDefault();
    const colDef = allColumnDefinitions.find(c => c.id === columnId);
    if (draggedHeaderId && draggedHeaderId !== columnId && colDef && colDef.isReorderable !== false) {
      setDragOverHeaderId(columnId);
      
      // 添加拖拽悬停的视觉反馈
      const targetElement = event.currentTarget;
      targetElement.classList.add('column-drag-over');
    }
  }, [draggedHeaderId, allColumnDefinitions]);

  const handleHeaderDragLeave = useCallback((event: React.DragEvent<HTMLTableCellElement>) => {
    // Only clear if leaving the item itself, not its children or to another valid drop target
    if (dragOverHeaderId && (!event.relatedTarget || !(event.currentTarget as Node).contains(event.relatedTarget as Node))) {
        setDragOverHeaderId(null);
        
        // 移除拖拽悬停的视觉反馈
        const targetElement = event.currentTarget;
        targetElement.classList.remove('column-drag-over');
    }
  }, [dragOverHeaderId]);

  const handleHeaderDrop = useCallback((event: React.DragEvent<HTMLTableCellElement>, targetColumnId: string) => {
    console.log('拖拽放置:', { draggedHeaderId, targetColumnId });
    event.preventDefault();
    if (!draggedHeaderId || draggedHeaderId === targetColumnId) {
      console.log('拖拽取消或目标相同');
      setDraggedHeaderId(null);
      setDragOverHeaderId(null);
      return;
    }
    const colDef = allColumnDefinitions.find(c => c.id === targetColumnId);
     if (!colDef || colDef.isReorderable === false) {
      setDraggedHeaderId(null);
      setDragOverHeaderId(null);
      return;
    }

    const newOrder = [...currentColumnOrder];
    const draggedIndex = newOrder.indexOf(draggedHeaderId);
    const targetIndex = newOrder.indexOf(targetColumnId);
    
    console.log('重新排序信息:', { 
      currentOrder: currentColumnOrder, 
      draggedIndex, 
      targetIndex, 
      draggedHeaderId, 
      targetColumnId 
    });

    if (draggedIndex === -1) { // Should not happen
        console.log('找不到拖拽的列索引');
        setDraggedHeaderId(null);
        setDragOverHeaderId(null);
        return;
    }
    
    const [movedItem] = newOrder.splice(draggedIndex, 1);
    newOrder.splice(targetIndex, 0, movedItem);
    
    console.log('新的列顺序:', newOrder);
    
    setCurrentColumnOrder(newOrder); // Update local state immediately for responsive UI
    onColumnOrderChange(newOrder); // Propagate change up

    // 添加拖拽完成的视觉反馈
    const targetElement = document.querySelector(`[data-column-id="${targetColumnId}"]`);
    if (targetElement) {
      targetElement.classList.remove('column-drag-over');
      targetElement.classList.add('column-drop-complete');
      setTimeout(() => {
        targetElement.classList.remove('column-drop-complete');
      }, 300);
    }
    
    // 清理拖拽状态
    const draggedElement = document.querySelector(`[data-column-id="${draggedHeaderId}"]`);
    if (draggedElement) {
      draggedElement.classList.remove('column-dragging');
      (draggedElement as HTMLElement).style.opacity = '';
    }
    document.body.classList.remove('column-reordering');

    setDraggedHeaderId(null);
    setDragOverHeaderId(null);
  }, [draggedHeaderId, currentColumnOrder, onColumnOrderChange, allColumnDefinitions]);

  const handleDragEndCapture = useCallback(() => {
    console.log('拖拽结束清理');
    // This ensures that if a drag ends outside a valid drop target (e.g. outside the window), state is reset
    setDraggedHeaderId(null);
    setDragOverHeaderId(null);
    
    // 清理所有拖拽相关的样式
    document.querySelectorAll('.column-dragging, .column-drag-over').forEach(el => {
      el.classList.remove('column-dragging', 'column-drag-over');
      (el as HTMLElement).style.opacity = '';
    });
    document.body.classList.remove('column-reordering');
    console.log('拖拽状态已清理');
  }, []);
  
  // 将所有交互处理器暴露到全局，以便表格组件可以访问
   useEffect(() => {
     if (typeof window !== 'undefined') {
       (window as any).tableColumnInteractions = {
         handleResizeMouseDown,
         handleResizeMouseMove,
         handleResizeMouseUp,
         handleHeaderDragStart,
         handleHeaderDragOver,
         handleHeaderDragEnter,
         handleHeaderDragLeave,
         handleHeaderDrop,
         handleDragEndCapture
       };
     }
     
     return () => {
       if (typeof window !== 'undefined') {
         delete (window as any).tableColumnInteractions;
       }
     };
   }, [handleResizeMouseDown, handleResizeMouseMove, handleResizeMouseUp, handleHeaderDragStart, handleHeaderDragOver, handleHeaderDragEnter, handleHeaderDragLeave, handleHeaderDrop, handleDragEndCapture]);


  return {
    currentColumnOrder,
    currentColumnWidths,
    resizingColumnId,
    draggedHeaderId,
    dragOverHeaderId,
    handleResizeMouseDown,
    handleResizeMouseMove,
    handleResizeMouseUp,
    handleHeaderDragStart,
    handleHeaderDragOver,
    handleHeaderDragEnter,
    handleHeaderDragLeave,
    handleHeaderDrop,
    handleDragEndCapture,
  };
}
