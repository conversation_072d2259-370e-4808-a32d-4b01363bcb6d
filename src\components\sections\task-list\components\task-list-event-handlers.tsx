// src/components/sections/task-list/components/task-list-event-handlers.tsx

import { useCallback } from 'react';
import type { Task, Vehicle, Plant } from '@/types';
import { useToast } from '@/hooks/use-toast';

interface TaskListEventHandlersProps {
  allTasks: Task[];
  plants: Plant[];
  selectedPlantId: string | null;
  dispatchVehicleToTask: (vehicleId: string, taskId: string, lineId: string) => Promise<Vehicle | null>;
  getStatusLabelProps: (status: string) => { label: string; variant: string };
}

interface UseTaskListEventHandlersReturn {
  handleDropOnProductionLine: (vehicle: Vehicle, taskId: string, lineId: string) => Promise<void>;
  handleOpenGroupConfig: () => void;
}

/**
 * 任务列表事件处理Hook
 * 集中管理任务列表的各种事件处理逻辑
 */
export function useTaskListEventHandlers({
  allTasks,
  plants,
  selectedPlantId,
  dispatchVehicleToTask,
  getStatusLabelProps,
}: TaskListEventHandlersProps): UseTaskListEventHandlersReturn {
  const { toast } = useToast();

  const handleDropOnProductionLine = useCallback(async (vehicle: Vehicle, taskId: string, lineId: string) => {
    const task = allTasks.find(t => t.id === taskId);
    if (!task) {
      toast({ title: "调度失败", description: "未找到目标任务。", variant: "destructive" });
      return;
    }
    if (task.dispatchStatus !== 'InProgress') {
      toast({ 
        title: "调度失败", 
        description: `任务 "${task.taskNumber}" 当前状态为 "${getStatusLabelProps(task.dispatchStatus).label}"，无法调度车辆。`, 
        variant: "destructive" 
      });
      return;
    }
    if (!vehicle) {
      toast({ title: "调度失败", description: "拖拽的车辆信息丢失。", variant: "destructive" });
      return;
    }

    const updatedVehicleResult = await dispatchVehicleToTask(vehicle.id, taskId, lineId);

    if (updatedVehicleResult) {
      const plantName = plants.find(p => p.id === selectedPlantId)?.name || selectedPlantId || 'N/A';
      toast({
        title: '车辆已调度',
        description: `车辆 ${updatedVehicleResult.vehicleNumber} 已成功调度到任务 ${task.taskNumber} (生产线 ${lineId}, 厂区: ${plantName}).`,
      });
    } else {
      toast({
        title: "调度失败",
        description: `无法调度车辆 ${vehicle.vehicleNumber} 到生产线 ${lineId}。请重试。`,
        variant: "destructive",
      });
    }
  }, [allTasks, dispatchVehicleToTask, toast, getStatusLabelProps, plants, selectedPlantId]);

  const handleOpenGroupConfig = useCallback(() => {
    // This will be handled by the parent component
    // We return a function that can be called by the parent
  }, []);

  return {
    handleDropOnProductionLine,
    handleOpenGroupConfig,
  };
}

/**
 * 任务列表拖拽处理Hook
 * 处理拖拽相关的事件和状态
 */
export function useTaskListDragHandlers() {
  const { toast } = useToast();

  const handleTaskListDrop = useCallback((item: any, monitor: any) => {
    if (!monitor.didDrop()) {
      toast({
        title: "操作无效",
        description: "请将车辆拖拽到具体的生产线上或厂区标签。",
        variant: "default",
      });
    }
  }, [toast]);

  return {
    handleTaskListDrop,
  };
}

/**
 * 任务列表表格事件处理Hook
 * 处理表格相关的事件
 */
export function useTaskListTableHandlers() {
  const handleHeaderContextMenu = useCallback((event: React.MouseEvent, columnDef: any) => {
    event.preventDefault();
    event.stopPropagation();
    // Handle header context menu logic
  }, []);

  const handleHeaderDoubleClick = useCallback((event: React.MouseEvent, columnDef: any) => {
    event.preventDefault();
    event.stopPropagation();
    // Handle header double click logic
  }, []);

  const handleRowContextMenu = useCallback((e: React.MouseEvent, row: any) => {
    // Handle row context menu logic
  }, []);

  const handleRowDoubleClick = useCallback((row: any) => {
    // Handle row double click logic
  }, []);

  return {
    handleHeaderContextMenu,
    handleHeaderDoubleClick,
    handleRowContextMenu,
    handleRowDoubleClick,
  };
}

/**
 * 任务列表FAB事件处理Hook
 * 处理浮动操作按钮的事件
 */
export function useTaskListFABHandlers() {
  const handleFabContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Handle FAB context menu logic
  }, []);

  return {
    handleFabContextMenu,
  };
}
