// src/contexts/ColumnDragDropContext.tsx
'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { ColumnOrderState, OnChangeFn } from '@tanstack/react-table';
import { CustomColumnDefinition } from '@/types';

interface ColumnDragDropContextType {
  // 拖拽状态
  draggedColumnId: string | null;
  dragOverColumnId: string | null;
  isDragging: boolean;
  
  // 拖拽约束
  isColumnReorderable: (columnId: string) => boolean;
  
  // 拖拽操作
  setDraggedColumnId: (id: string | null) => void;
  setDragOverColumnId: (id: string | null) => void;
  setIsDragging: (isDragging: boolean) => void;
  
  // 列排序持久化
  columnOrder: string[];
  updateColumnOrder: (newOrder: string[]) => void;
  
  // 拖拽预览
  previewColumnOrder: string[] | null;
  setPreviewColumnOrder: (order: string[] | null) => void;
  
  // 列定义映射
  columnDefinitions: Record<string, CustomColumnDefinition>;
}

export const ColumnDragDropContext = createContext<ColumnDragDropContextType | undefined>(undefined);

interface ColumnDragDropProviderProps {
  children: ReactNode;
  columnOrder: string[];
  onColumnOrderChange?: OnChangeFn<ColumnOrderState>;
  columnDefinitions: CustomColumnDefinition[];
}

export const ColumnDragDropProvider: React.FC<ColumnDragDropProviderProps> = ({
  children,
  columnOrder,
  onColumnOrderChange,
  columnDefinitions,
}) => {
  // 拖拽状态
  const [draggedColumnId, setDraggedColumnId] = useState<string | null>(null);
  const [dragOverColumnId, setDragOverColumnId] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  
  // 拖拽预览
  const [previewColumnOrder, setPreviewColumnOrder] = useState<string[] | null>(null);
  
  // 创建列定义映射，方便快速查找
  const columnDefinitionsMap = columnDefinitions.reduce<Record<string, CustomColumnDefinition>>(
    (acc, col) => {
      acc[col.id] = col;
      return acc;
    },
    {}
  );
  
  // 检查列是否可重新排序
  const isColumnReorderable = useCallback(
    (columnId: string) => {
      const column = columnDefinitionsMap[columnId];
      return column?.isReorderable !== false;
    },
    [columnDefinitionsMap]
  );
  
  // 更新列顺序并持久化
  const updateColumnOrder = useCallback(
    (newOrder: string[]) => {
      if (onColumnOrderChange) {
        onColumnOrderChange(newOrder);
      }
      // 清除预览
      setPreviewColumnOrder(null);
    },
    [onColumnOrderChange]
  );
  
  const value = {
    draggedColumnId,
    dragOverColumnId,
    isDragging,
    isColumnReorderable,
    setDraggedColumnId,
    setDragOverColumnId,
    setIsDragging,
    columnOrder,
    updateColumnOrder,
    previewColumnOrder,
    setPreviewColumnOrder,
    columnDefinitions: columnDefinitionsMap,
  };
  
  return (
    <ColumnDragDropContext.Provider value={value}>
      {children}
    </ColumnDragDropContext.Provider>
  );
};

export const useColumnDragDropContext = () => {
  const context = useContext(ColumnDragDropContext);
  if (context === undefined) {
    throw new Error('useColumnDragDropContext must be used within a ColumnDragDropProvider');
  }
  return context;
};