/**
 * 提供声音提醒相关的工具函数
 */

// 播放提醒声音
export const playReminderSound = (soundId: string = 'dispatch-alert-sound'): Promise<void> => {
  return new Promise((resolve, reject) => {
    const audio = document.getElementById(soundId) as HTMLAudioElement;
    if (!audio) {
      console.warn(`找不到ID为${soundId}的音频元素`);
      reject(new Error(`找不到ID为${soundId}的音频元素`));
      return;
    }

    audio.play()
      .then(() => resolve())
      .catch(error => {
        console.warn("播放音频失败:", error);
        reject(error);
      });
  });
};

// 停止提醒声音
export const stopReminderSound = (soundId: string = 'dispatch-alert-sound'): void => {
  const audio = document.getElementById(soundId) as HTMLAudioElement;
  if (audio) {
    audio.pause();
    audio.currentTime = 0;
  }
}; 