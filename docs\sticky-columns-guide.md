# 固定列功能使用指南

## 概述

本项目的固定列功能提供了完整的左右固定列支持，包括阴影效果、类型安全和统一的样式管理。

## 文件结构

```
src/
├── types/
│   └── sticky-columns.ts          # 固定列相关类型定义
├── utils/
│   └── sticky-columns.ts          # 固定列工具函数
├── styles/
│   ├── sticky-columns.css         # 固定列样式
│   └── z-index-standards.md       # z-index层级规范
└── components/ui/
    └── virtualized-table.tsx      # 表格组件实现
```

## 核心特性

### 1. 类型安全

使用 TypeScript 严格类型定义，确保配置的正确性：

```typescript
import { StickyColumnConfig, StickyColumnPosition } from '@/types/sticky-columns';

const stickyConfig: StickyColumnConfig = {
  columnId: 'vehicle-id',
  position: StickyColumnPosition.LEFT,
  order: 0,
  minWidth: 120,
  maxWidth: 200,
  showShadow: true
};
```

### 2. 统一样式管理

所有固定列样式集中在 `sticky-columns.css` 中：

```css
/* 基础固定列样式 */
.sticky-column-base {
  background-color: var(--sticky-bg-color);
  border-right: var(--sticky-border);
}

/* 左固定列阴影 */
.sticky-col-shadow-right {
  box-shadow: var(--sticky-shadow-right);
}
```

### 3. Z-Index 层级管理

遵循统一的 z-index 规范：

- **基础内容**: 1-99
- **固定元素**: 100-199
  - 固定列内容: 150
  - 固定列表头: 160
  - 固定列阴影: 170
- **弹出层**: 200-299
- **系统层**: 300+

## 使用方法

### 1. 基础配置

```typescript
import { StickyColumnUtils, StickyColumnPosition } from '@/utils/sticky-columns';

// 创建固定列配置
const leftColumns = [
  {
    columnId: 'vehicle-id',
    position: StickyColumnPosition.LEFT,
    order: 0,
    showShadow: true
  },
  {
    columnId: 'driver-name',
    position: StickyColumnPosition.LEFT,
    order: 1,
    showShadow: false
  }
];

const rightColumns = [
  {
    columnId: 'actions',
    position: StickyColumnPosition.RIGHT,
    order: 0,
    showShadow: true
  }
];
```

### 2. 样式计算

```typescript
// 计算左固定列偏移量
const leftOffset = StickyColumnUtils.calculateLeftOffset(
  'driver-name',
  leftColumns,
  (id) => getColumnWidth(id)
);

// 生成固定列样式
const stickyStyles = StickyColumnUtils.generateStickyStyles(
  columnConfig,
  isHeader,
  leftOffset,
  columnWidth
);
```

### 3. 阴影配置

```typescript
// 使用默认阴影配置
const defaultShadow = StickyColumnUtils.createDefaultShadowConfig(
  StickyColumnPosition.LEFT
);

// 自定义阴影配置
const customShadow = StickyColumnUtils.mergeShadowConfig(
  defaultShadow,
  {
    blur: 20,
    color: 'rgba(0, 0, 0, 0.4)'
  }
);

// 响应式阴影（移动端优化）
const responsiveShadow = StickyColumnUtils.getResponsiveShadowConfig(
  StickyColumnPosition.LEFT,
  isMobile
);
```

### 4. CSS 类名生成

```typescript
// 生成CSS类名
const classNames = StickyColumnUtils.generateClassNames(
  'vehicle-id',
  StickyColumnPosition.LEFT,
  true // 显示阴影
);
// 结果: ['sticky-column-base', 'sticky-column-left', 'sticky-col-shadow-right']
```

## 最佳实践

### 1. 配置验证

始终验证固定列配置的有效性：

```typescript
const isValid = StickyColumnUtils.validateStickyConfig(columnConfig);
if (!isValid) {
  console.error('固定列配置无效');
  return;
}
```

### 2. 性能优化

- 缓存计算结果，避免重复计算偏移量
- 使用 `useMemo` 缓存样式对象
- 合理设置固定列数量，避免过多固定列影响性能

```typescript
const stickyStyles = useMemo(() => {
  return StickyColumnUtils.generateStickyStyles(
    columnConfig,
    isHeader,
    offset,
    columnWidth
  );
}, [columnConfig, isHeader, offset, columnWidth]);
```

### 3. 响应式设计

根据屏幕尺寸调整固定列配置：

```typescript
const isMobile = useMediaQuery('(max-width: 768px)');

// 移动端减少固定列数量
const effectiveLeftColumns = isMobile 
  ? leftColumns.slice(0, 1) 
  : leftColumns;
```

### 4. 主题支持

使用 CSS 变量支持主题切换：

```css
:root {
  --sticky-bg-color: #ffffff;
  --sticky-border: 1px solid #e5e7eb;
  --sticky-shadow-right: 8px 0 15px -3px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] {
  --sticky-bg-color: #1f2937;
  --sticky-border: 1px solid #374151;
  --sticky-shadow-right: 8px 0 15px -3px rgba(0, 0, 0, 0.5);
}
```

## 故障排除

### 1. 阴影不显示

- 检查 z-index 层级是否正确
- 确认阴影CSS类是否正确应用
- 验证阴影配置参数

### 2. 固定列位置错误

- 检查偏移量计算是否正确
- 确认列宽度获取函数是否正常工作
- 验证固定列排序配置

### 3. 性能问题

- 减少固定列数量
- 使用 `useMemo` 缓存计算结果
- 优化列宽度计算逻辑

## 扩展功能

### 1. 自定义阴影动画

```css
.sticky-col-shadow-right {
  transition: box-shadow 0.2s ease-in-out;
}

.sticky-col-shadow-right:hover {
  box-shadow: var(--sticky-shadow-right-hover);
}
```

### 2. 固定列拖拽排序

```typescript
// 实现固定列顺序调整
const handleColumnReorder = (dragIndex: number, hoverIndex: number) => {
  const newColumns = [...leftColumns];
  const dragColumn = newColumns[dragIndex];
  newColumns.splice(dragIndex, 1);
  newColumns.splice(hoverIndex, 0, dragColumn);
  
  // 更新order属性
  newColumns.forEach((col, index) => {
    col.order = index;
  });
  
  setLeftColumns(newColumns);
};
```

### 3. 固定列宽度调整

```typescript
// 实现固定列宽度调整
const handleColumnResize = (columnId: string, newWidth: number) => {
  const config = leftColumns.find(col => col.columnId === columnId);
  if (config) {
    // 验证宽度范围
    const clampedWidth = Math.max(
      config.minWidth || 50,
      Math.min(config.maxWidth || 500, newWidth)
    );
    
    // 更新列宽度
    updateColumnWidth(columnId, clampedWidth);
  }
};
```

## 版本历史

- **v1.0.0**: 基础固定列功能
- **v1.1.0**: 添加阴影效果
- **v1.2.0**: 类型安全和工具函数
- **v1.3.0**: 统一样式管理和z-index规范

## 贡献指南

1. 遵循现有的代码风格和命名约定
2. 添加适当的类型定义
3. 更新相关文档
4. 添加单元测试
5. 确保向后兼容性