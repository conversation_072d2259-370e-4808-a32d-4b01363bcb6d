// src/components/sections/task-list/task-list.config.ts
import type { CustomColumnDefinition } from '@/types';

export const ALL_TASK_COLUMNS_CONFIG: CustomColumnDefinition[] = [
  {
    id: 'messages', label: '消息', defaultVisible: true, isReorderable: false, isResizable: false, defaultWidth: 50, minWidth: 50, isStyleable: true, fixed: 'left', order: 1,
    densityStyles: undefined
  },
  {
    id: 'dispatchReminder', label: '发车提醒', defaultVisible: true, isReorderable: false, isResizable: false, defaultWidth: 100, minWidth: 100, isStyleable: true, fixed: 'left', order: 2,
    densityStyles: undefined
  },
  {
    id: 'taskNumber', label: '任务编号', defaultVisible: true, isReorderable: true, isResizable: true, defaultWidth: 60, minWidth: 60, isStyleable: true, order: 3,
    densityStyles: undefined
  },
  {
    id: 'projectName', label: '工程名称', isMandatory: true, defaultVisible: true, isReorderable: true, isResizable: true, defaultWidth: 200, minWidth: 120, maxWidth: 380, isStyleable: true, order: 4,
    densityStyles: undefined
  },
  {
    id: 'projectAbbreviation', label: '任务缩写', defaultVisible: false, isReorderable: true, isResizable: true, defaultWidth: 100, minWidth: 70, isStyleable: true, order: 5,
    densityStyles: undefined
  },
  {
    id: 'constructionUnit', label: '施工单位', isMandatory: true, defaultVisible: true, isReorderable: true, isResizable: true, defaultWidth: 180, minWidth: 120, maxWidth: 380, isStyleable: true, order: 6,
    densityStyles: undefined
  },
  {
    id: 'constructionSite', label: '施工部位', isMandatory: true, defaultVisible: true, isReorderable: true, isResizable: true, defaultWidth: 150, minWidth: 100, isStyleable: true, order: 7,
    densityStyles: undefined
  },
  {
    id: 'strength', label: '强度', defaultVisible: true, isReorderable: true, isResizable: true, defaultWidth: 50, minWidth: 30, isStyleable: true, order: 8,
    densityStyles: undefined
  },
  {
    id: 'pouringMethod', label: '浇筑方式', defaultVisible: true, isReorderable: true, isResizable: true, defaultWidth: 60, minWidth: 60, isStyleable: true, order: 9,
    densityStyles: undefined
  },
  {
    id: 'vehicleCount', label: '车数', defaultVisible: true, isReorderable: true, isResizable: true, defaultWidth: 30, minWidth: 30, isStyleable: true, order: 10,
    densityStyles: undefined
  },
  {
    id: 'completedProgress', label: '进度', defaultVisible: true, isReorderable: true, isResizable: true, defaultWidth: 80, minWidth: 80, isStyleable: true, order: 11,
    densityStyles: undefined
  },
  {
    id: 'requiredVolume', label: '需供', defaultVisible: false, isReorderable: true, isResizable: true, defaultWidth: 70, minWidth: 40, isStyleable: true, order: 12,
    densityStyles: undefined
  },
  {
    id: 'completedVolume', label: '已供', defaultVisible: false, isReorderable: true, isResizable: true, defaultWidth: 70, minWidth: 40, isStyleable: true, order: 13,
    densityStyles: undefined
  },
  {
    id: 'pumpTruck', label: '泵车', defaultVisible: false, isReorderable: true, isResizable: true, defaultWidth: 60, minWidth: 60, isStyleable: true, order: 14,
    densityStyles: undefined
  },
  {
    id: 'otherRequirements', label: '其他要求', defaultVisible: false, isReorderable: true, isResizable: true, defaultWidth: 150, minWidth: 100, isStyleable: true, order: 15,
    densityStyles: undefined
  },
  {
    id: 'contactPhone', label: '联系电话', defaultVisible: false, isReorderable: true, isResizable: true, defaultWidth: 120, minWidth: 90, isStyleable: true, order: 16,
    densityStyles: undefined
  },
  {
    id: 'supplyTime', label: '供砼时间', defaultVisible: true, isReorderable: true, isResizable: true, defaultWidth: 90, minWidth: 60, isStyleable: true, order: 17,
    densityStyles: undefined
  },
  {
    id: 'supplyDate', label: '供砼日期', defaultVisible: true, isReorderable: true, isResizable: true, defaultWidth: 100, minWidth: 70, isStyleable: true, order: 18,
    densityStyles: undefined
  },
  {
    id: 'publishDate', label: '发布日期', defaultVisible: false, isReorderable: true, isResizable: true, defaultWidth: 100, minWidth: 70, isStyleable: true, order: 19,
    densityStyles: undefined
  },
  {
    id: 'timing', label: '计时', defaultVisible: false, isReorderable: true, isResizable: true, defaultWidth: 80, minWidth: 60, isStyleable: true, order: 20,
    densityStyles: undefined
  },
  {
    id: 'dispatchedVehicles',
    label: '调度车辆',
    defaultVisible: true,
    isReorderable: true,
    isResizable: true,
    defaultWidth: 320,
    minWidth: 220,
    maxWidth: 600,
    fixed: 'right', 
    order: 21, 
    densityStyles: undefined,
    isStyleable: true, // Ensure this can receive background styles
  },
  {
    id: 'productionLines', label: '生产线', isMandatory: true, defaultVisible: true, isReorderable: false, isResizable: false, defaultWidth: 88, minWidth: 88, isStyleable: true, fixed: 'right', order: 22,
    densityStyles: undefined
  },
];

// Ensure all order values are unique and sequential starting from 1
let currentOrderValue = 1;
const orderedColumns = ALL_TASK_COLUMNS_CONFIG.sort((a, b) => (a.order || 999) - (b.order || 999));
orderedColumns.forEach(col => {
    col.order = currentOrderValue++;
});
