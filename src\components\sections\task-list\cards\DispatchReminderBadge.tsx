'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { AlertTriangle, Clock, Bell } from 'lucide-react';

interface DispatchReminderBadgeProps {
  minutes: number;
  size?: 'small' | 'medium' | 'large' | 'extra-large';
  variant?: 'default' | 'outline' | 'solid';
  animated?: boolean;
}

export const DispatchReminderBadge: React.FC<DispatchReminderBadgeProps> = ({
  minutes,
  size = 'medium',
  variant = 'default',
  animated = true,
}) => {
  // 获取紧急程度配置
  const getUrgencyConfig = () => {
    if (minutes <= 5) {
      return {
        level: 'critical',
        icon: <AlertTriangle className="w-full h-full" />,
        className: 'bg-red-500 text-white border-red-600',
        glowColor: 'shadow-red-500/50',
        pulseColor: 'bg-red-400',
      };
    } else if (minutes <= 15) {
      return {
        level: 'warning',
        icon: <Clock className="w-full h-full" />,
        className: 'bg-orange-500 text-white border-orange-600',
        glowColor: 'shadow-orange-500/50',
        pulseColor: 'bg-orange-400',
      };
    } else {
      return {
        level: 'normal',
        icon: <Bell className="w-full h-full" />,
        className: 'bg-blue-500 text-white border-blue-600',
        glowColor: 'shadow-blue-500/50',
        pulseColor: 'bg-blue-400',
      };
    }
  };

  // 获取尺寸样式
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          container: 'px-2 py-1 text-xs',
          icon: 'w-3 h-3',
          gap: 'gap-1',
          pulse: 'w-8 h-6',
        };
      case 'large':
        return {
          container: 'px-4 py-2 text-base',
          icon: 'w-5 h-5',
          gap: 'gap-2',
          pulse: 'w-16 h-10',
        };
      case 'extra-large':
        return {
          container: 'px-5 py-2.5 text-lg',
          icon: 'w-6 h-6',
          gap: 'gap-2.5',
          pulse: 'w-20 h-12',
        };
      default: // medium
        return {
          container: 'px-3 py-1.5 text-sm',
          icon: 'w-4 h-4',
          gap: 'gap-1.5',
          pulse: 'w-12 h-8',
        };
    }
  };

  const urgencyConfig = getUrgencyConfig();
  const sizeStyles = getSizeStyles();

  // 格式化时间显示
  const formatTime = () => {
    if (minutes <= 5) {
      const mins = Math.floor(minutes);
      const secs = Math.round((minutes % 1) * 60);
      return `${mins}:${secs.toString().padStart(2, '0')}`;
    }
    return `${Math.round(minutes)}分钟`;
  };

  // 基础样式
  const baseStyles = cn(
    "relative inline-flex items-center font-bold rounded-full transition-all duration-200",
    "border-2 shadow-lg shadow-right dark:shadow-right",
    sizeStyles.container,
    sizeStyles.gap
  );

  // 根据变体返回不同样式
  if (variant === 'outline') {
    return (
      <div className={cn(
        baseStyles,
        "bg-transparent border-current",
        urgencyConfig.level === 'critical' ? 'text-red-500 border-red-500' :
        urgencyConfig.level === 'warning' ? 'text-orange-500 border-orange-500' :
        'text-blue-500 border-blue-500'
      )}>
        <div className={cn("flex-shrink-0", sizeStyles.icon)}>
          {urgencyConfig.icon}
        </div>
        <span>距发: {formatTime()}</span>
      </div>
    );
  }

  if (variant === 'solid') {
    return (
      <div className={cn(
        baseStyles,
        urgencyConfig.className,
        urgencyConfig.glowColor,
        animated && urgencyConfig.level === 'critical' && "animate-pulse"
      )}>
        <div className={cn("flex-shrink-0", sizeStyles.icon)}>
          {urgencyConfig.icon}
        </div>
        <span>距发: {formatTime()}</span>
      </div>
    );
  }

  // Default variant with enhanced effects
  return (
    <div className="relative">
      {/* 脉冲背景动画 */}
      {animated && urgencyConfig.level === 'critical' && (
        <div className={cn(
          "absolute inset-0 rounded-full animate-ping",
          sizeStyles.pulse,
          urgencyConfig.pulseColor,
          "opacity-75"
        )} />
      )}
      
      {/* 发光效果 */}
      {animated && (
        <div className={cn(
          "absolute inset-0 rounded-full blur-sm",
          urgencyConfig.className,
          "opacity-50 animate-pulse"
        )} />
      )}

      {/* 主要徽章 */}
      <div className={cn(
        baseStyles,
        urgencyConfig.className,
        urgencyConfig.glowColor,
        "relative z-10",
        animated && "hover:scale-110",
        urgencyConfig.level === 'critical' && animated && "animate-bounce"
      )}>
        <div className={cn("flex-shrink-0", sizeStyles.icon)}>
          {urgencyConfig.icon}
        </div>
        <span className="whitespace-nowrap">
          {urgencyConfig.level === 'critical' ? formatTime() : `距发: ${formatTime()}`}
        </span>
      </div>

      {/* 额外的视觉效果 */}
      {urgencyConfig.level === 'critical' && animated && (
        <>
          {/* 闪烁边框 */}
          <div className={cn(
            "absolute inset-0 rounded-full border-2 border-red-300 animate-ping",
            "opacity-60"
          )} />
          
          {/* 旋转光环 */}
          <div
            className={cn(
              "absolute inset-0 rounded-full",
              "bg-gradient-to-r from-red-400 via-transparent to-red-400",
              "animate-spin opacity-30"
            )}
            style={{ animationDuration: '2s' }}
          />
        </>
      )}
    </div>
  );
};
