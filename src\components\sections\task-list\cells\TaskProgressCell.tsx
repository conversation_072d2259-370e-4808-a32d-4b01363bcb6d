
// src/components/sections/task-list/cells/TaskProgressCell.tsx
'use client';

import React, { memo } from 'react';
import type { Task } from '@/types';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

interface TaskProgressCellProps {
  progressValue: number;
  textClassName?: string;
}

const TaskProgressCellComponent: React.FC<TaskProgressCellProps> = ({ progressValue, textClassName }) => {
  return (
    <div className="flex flex-col items-center justify-center h-full">
      <Progress value={progressValue} className="w-full h-2.5" />
      <span className={cn("text-[9px] mt-0.5", textClassName)}>{Math.round(progressValue)}%</span>
    </div>
  );
};

export const TaskProgressCell = memo(TaskProgressCellComponent);
